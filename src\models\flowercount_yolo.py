"""
FlowerCount-YOLO: Advanced Flower Detection and Counting Model

This module implements the core architecture for the FlowerCount-YOLO model,
featuring multi-scale attention mechanisms and density estimation for accurate
flower detection and counting.

Paper: "FlowerCount-YOLO: A Novel Multi-Scale Attention-Enhanced Framework 
       for Accurate Flower Detection and Counting"
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional
import math

from ultralytics.models.yolo.detect import DetectionModel
from ultralytics.nn.modules import Conv, C2f, SPPF, Detect


class CBAM(nn.Module):
    """
    Convolutional Block Attention Module (CBAM)
    
    Combines channel attention and spatial attention mechanisms
    to enhance feature representation for flower detection.
    """
    
    def __init__(self, channels: int, reduction: int = 16, kernel_size: int = 7):
        super(CBAM, self).__init__()
        
        # Channel Attention Module
        self.channel_attention = ChannelAttention(channels, reduction)
        
        # Spatial Attention Module  
        self.spatial_attention = SpatialAttention(kernel_size)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Apply channel attention
        x = self.channel_attention(x) * x
        
        # Apply spatial attention
        x = self.spatial_attention(x) * x
        
        return x


class ChannelAttention(nn.Module):
    """Channel Attention Module for CBAM"""
    
    def __init__(self, channels: int, reduction: int = 16):
        super(ChannelAttention, self).__init__()
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)


class SpatialAttention(nn.Module):
    """Spatial Attention Module for CBAM"""
    
    def __init__(self, kernel_size: int = 7):
        super(SpatialAttention, self).__init__()
        
        padding = kernel_size // 2
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv(x)
        return self.sigmoid(x)


class MultiScaleFusion(nn.Module):
    """
    Multi-Scale Feature Fusion Module
    
    Enhances feature representation by fusing information from multiple scales
    using Feature Pyramid Network (FPN) and Path Aggregation Network (PAN).
    """
    
    def __init__(self, channels: List[int]):
        super(MultiScaleFusion, self).__init__()
        
        self.channels = channels
        
        # Top-down pathway (FPN)
        self.fpn_convs = nn.ModuleList([
            nn.Conv2d(channels[i], channels[0], 1) 
            for i in range(len(channels))
        ])
        
        # Bottom-up pathway (PAN)
        self.pan_convs = nn.ModuleList([
            nn.Conv2d(channels[0], channels[0], 3, padding=1)
            for _ in range(len(channels))
        ])
        
        # Attention modules for each scale
        self.attention_modules = nn.ModuleList([
            CBAM(channels[0]) for _ in range(len(channels))
        ])
        
    def forward(self, features: List[torch.Tensor]) -> List[torch.Tensor]:
        # Normalize feature dimensions
        normalized_features = []
        for i, feat in enumerate(features):
            normalized_features.append(self.fpn_convs[i](feat))
        
        # Top-down pathway
        for i in range(len(normalized_features) - 2, -1, -1):
            upsampled = F.interpolate(
                normalized_features[i + 1], 
                size=normalized_features[i].shape[2:], 
                mode='nearest'
            )
            normalized_features[i] = normalized_features[i] + upsampled
        
        # Bottom-up pathway with attention
        enhanced_features = []
        for i, feat in enumerate(normalized_features):
            enhanced = self.pan_convs[i](feat)
            enhanced = self.attention_modules[i](enhanced)
            enhanced_features.append(enhanced)
        
        return enhanced_features


class DensityEstimationHead(nn.Module):
    """
    Density Estimation Head for Flower Counting
    
    Generates density maps to estimate flower counts in crowded scenes
    where individual detection might be challenging.
    """
    
    def __init__(self, in_channels: int, hidden_channels: int = 256):
        super(DensityEstimationHead, self).__init__()
        
        self.conv_layers = nn.Sequential(
            nn.Conv2d(in_channels, hidden_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_channels, hidden_channels // 2, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_channels // 2, hidden_channels // 4, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_channels // 4, 1, 1),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        density_map = self.conv_layers(x)
        return density_map


class FlowerCountYOLO(DetectionModel):
    """
    FlowerCount-YOLO: Advanced Flower Detection and Counting Model
    
    This model extends YOLOv10 with:
    1. Multi-scale attention mechanisms (CBAM)
    2. Enhanced feature fusion (FPN + PAN)
    3. Density estimation for counting
    4. Progressive training strategy
    """
    
    def __init__(self, cfg: str = None, ch: int = 3, nc: int = 1, verbose: bool = True):
        super().__init__(cfg, ch, nc, verbose)
        
        # Initialize innovation components
        self.multi_scale_fusion = None
        self.density_head = None
        self.attention_modules = nn.ModuleList()
        
        # Configuration
        self.enable_density = True
        self.enable_attention = True
        
        self._initialize_innovations()
        
    def _initialize_innovations(self):
        """Initialize the innovation components"""
        
        # Get feature channels from backbone
        feature_channels = self._get_feature_channels()
        
        # Initialize multi-scale fusion
        if len(feature_channels) > 1:
            self.multi_scale_fusion = MultiScaleFusion(feature_channels)
        
        # Initialize density estimation head
        if self.enable_density and feature_channels:
            self.density_head = DensityEstimationHead(feature_channels[0])
        
        # Add attention modules to existing layers
        if self.enable_attention:
            self._add_attention_modules()
    
    def _get_feature_channels(self) -> List[int]:
        """Extract feature channel dimensions from the model"""
        channels = []
        
        # This is a simplified version - in practice, you'd extract
        # actual channel dimensions from the YOLOv10 backbone
        # For now, using typical YOLOv10 channel dimensions
        channels = [256, 512, 1024]  # P3, P4, P5 feature levels
        
        return channels
    
    def _add_attention_modules(self):
        """Add attention modules to key layers"""
        
        # Add CBAM modules to neck layers
        for i, module in enumerate(self.model):
            if isinstance(module, (C2f, Conv)):
                # Add attention after certain layers
                if hasattr(module, 'cv2'):  # C2f module
                    channels = module.cv2.conv.out_channels
                    attention = CBAM(channels)
                    self.attention_modules.append(attention)
    
    def forward(self, x: torch.Tensor, **kwargs) -> Dict[str, torch.Tensor]:
        """
        Forward pass with enhanced features
        
        Args:
            x: Input tensor [B, C, H, W]
            
        Returns:
            Dictionary containing:
            - detections: Standard YOLO detection outputs
            - density_map: Density estimation map (if enabled)
            - attention_maps: Attention visualizations (if requested)
        """
        
        # Standard YOLO forward pass
        detection_output = super().forward(x, **kwargs)
        
        results = {'detections': detection_output}
        
        # Extract features for additional heads
        if self.enable_density or 'return_features' in kwargs:
            features = self._extract_features(x)
            
            # Apply multi-scale fusion if available
            if self.multi_scale_fusion is not None:
                features = self.multi_scale_fusion(features)
            
            # Generate density map
            if self.enable_density and self.density_head is not None:
                density_map = self.density_head(features[0])  # Use highest resolution
                results['density_map'] = density_map
                
                # Calculate total count from density map
                total_count = torch.sum(density_map, dim=[2, 3])
                results['predicted_count'] = total_count
        
        return results
    
    def _extract_features(self, x: torch.Tensor) -> List[torch.Tensor]:
        """Extract multi-scale features from the backbone"""
        
        features = []
        
        # This is a simplified feature extraction
        # In practice, you'd hook into specific layers of YOLOv10
        for i, module in enumerate(self.model):
            x = module(x)
            
            # Collect features from specific layers (P3, P4, P5)
            if i in [6, 9, 12]:  # Example layer indices
                features.append(x)
        
        return features
    
    def get_attention_maps(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Generate attention maps for visualization"""
        
        attention_maps = {}
        
        # Hook into attention modules to extract attention weights
        def hook_fn(name):
            def hook(module, input, output):
                if hasattr(module, 'channel_attention'):
                    attention_maps[f'{name}_channel'] = module.channel_attention(input[0])
                if hasattr(module, 'spatial_attention'):
                    attention_maps[f'{name}_spatial'] = module.spatial_attention(input[0])
            return hook
        
        # Register hooks
        hooks = []
        for i, attention_module in enumerate(self.attention_modules):
            hook = attention_module.register_forward_hook(hook_fn(f'attention_{i}'))
            hooks.append(hook)
        
        # Forward pass
        with torch.no_grad():
            _ = self.forward(x)
        
        # Remove hooks
        for hook in hooks:
            hook.remove()
        
        return attention_maps


def create_flowercount_yolo(config_path: str = None, **kwargs) -> FlowerCountYOLO:
    """
    Factory function to create FlowerCount-YOLO model
    
    Args:
        config_path: Path to model configuration file
        **kwargs: Additional model parameters
        
    Returns:
        FlowerCountYOLO model instance
    """
    
    model = FlowerCountYOLO(cfg=config_path, **kwargs)
    
    return model
