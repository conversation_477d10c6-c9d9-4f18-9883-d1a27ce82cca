"""
Comprehensive Evaluation Metrics for FlowerCount-YOLO

This module implements a complete evaluation framework including:
1. Detection metrics (mAP, Precision, Recall, F1)
2. Counting metrics (MAE, RMSE, MAPE, R²)
3. Density estimation metrics (MSE, SSIM)
4. Statistical analysis and visualization

Paper: "FlowerCount-YOLO: A Novel Multi-Scale Attention-Enhanced Framework 
       for Accurate Flower Detection and Counting"
"""

import numpy as np
import torch
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from scipy.stats import pearsonr, spearmanr
import cv2
from pathlib import Path
import json
import pandas as pd


class FlowerCountingMetrics:
    """
    Comprehensive metrics calculator for flower detection and counting
    
    Provides evaluation metrics for:
    - Object detection performance
    - Counting accuracy
    - Density estimation quality
    - Statistical analysis
    """
    
    def __init__(self, iou_thresholds: List[float] = None):
        
        if iou_thresholds is None:
            self.iou_thresholds = [0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95]
        else:
            self.iou_thresholds = iou_thresholds
        
        # Initialize metric storage
        self.reset()
        
    def reset(self):
        """Reset all metrics"""
        self.predictions = []
        self.ground_truths = []
        self.detection_results = []
        self.counting_results = []
        self.density_results = []
        
    def add_batch(self, 
                  predictions: Dict, 
                  ground_truths: Dict,
                  image_ids: List[str] = None):
        """
        Add a batch of predictions and ground truths
        
        Args:
            predictions: Dictionary containing model predictions
            ground_truths: Dictionary containing ground truth annotations
            image_ids: List of image identifiers
        """
        
        batch_size = len(predictions.get('boxes', []))
        
        for i in range(batch_size):
            image_id = image_ids[i] if image_ids else f"image_{len(self.predictions)}"
            
            # Extract predictions for this image
            pred_data = {
                'image_id': image_id,
                'boxes': predictions.get('boxes', [])[i] if predictions.get('boxes') else [],
                'scores': predictions.get('scores', [])[i] if predictions.get('scores') else [],
                'labels': predictions.get('labels', [])[i] if predictions.get('labels') else [],
                'predicted_count': predictions.get('predicted_count', [0])[i] if predictions.get('predicted_count') is not None else 0,
                'density_map': predictions.get('density_map', [])[i] if predictions.get('density_map') else None
            }
            
            # Extract ground truths for this image
            gt_data = {
                'image_id': image_id,
                'boxes': ground_truths.get('boxes', [])[i] if ground_truths.get('boxes') else [],
                'labels': ground_truths.get('labels', [])[i] if ground_truths.get('labels') else [],
                'actual_count': ground_truths.get('actual_count', [0])[i] if ground_truths.get('actual_count') is not None else len(ground_truths.get('boxes', [])[i] if ground_truths.get('boxes') else []),
                'density_map': ground_truths.get('density_map', [])[i] if ground_truths.get('density_map') else None
            }
            
            self.predictions.append(pred_data)
            self.ground_truths.append(gt_data)
    
    def calculate_detection_metrics(self) -> Dict[str, float]:
        """Calculate object detection metrics"""
        
        all_pred_boxes = []
        all_pred_scores = []
        all_gt_boxes = []
        
        # Collect all predictions and ground truths
        for pred, gt in zip(self.predictions, self.ground_truths):
            if len(pred['boxes']) > 0:
                all_pred_boxes.extend(pred['boxes'])
                all_pred_scores.extend(pred['scores'])
            
            if len(gt['boxes']) > 0:
                all_gt_boxes.extend(gt['boxes'])
        
        if not all_pred_boxes or not all_gt_boxes:
            return {
                'mAP@0.5': 0.0,
                'mAP@0.5:0.95': 0.0,
                'Precision': 0.0,
                'Recall': 0.0,
                'F1-Score': 0.0
            }
        
        # Calculate mAP for different IoU thresholds
        map_scores = []
        
        for iou_thresh in self.iou_thresholds:
            ap = self._calculate_average_precision(
                all_pred_boxes, all_pred_scores, all_gt_boxes, iou_thresh
            )
            map_scores.append(ap)
        
        # Calculate precision, recall, F1 at IoU=0.5
        precision, recall, f1 = self._calculate_precision_recall_f1(
            all_pred_boxes, all_pred_scores, all_gt_boxes, iou_threshold=0.5
        )
        
        return {
            'mAP@0.5': map_scores[0],
            'mAP@0.5:0.95': np.mean(map_scores),
            'Precision': precision,
            'Recall': recall,
            'F1-Score': f1
        }
    
    def calculate_counting_metrics(self) -> Dict[str, float]:
        """Calculate counting accuracy metrics"""
        
        predicted_counts = []
        actual_counts = []
        
        for pred, gt in zip(self.predictions, self.ground_truths):
            predicted_counts.append(pred['predicted_count'])
            actual_counts.append(gt['actual_count'])
        
        if not predicted_counts:
            return {
                'MAE': float('inf'),
                'RMSE': float('inf'),
                'MAPE': float('inf'),
                'R²': 0.0,
                'Pearson_r': 0.0,
                'Spearman_r': 0.0
            }
        
        predicted_counts = np.array(predicted_counts)
        actual_counts = np.array(actual_counts)
        
        # Mean Absolute Error
        mae = mean_absolute_error(actual_counts, predicted_counts)
        
        # Root Mean Square Error
        rmse = np.sqrt(mean_squared_error(actual_counts, predicted_counts))
        
        # Mean Absolute Percentage Error
        mape = np.mean(np.abs((actual_counts - predicted_counts) / (actual_counts + 1e-8))) * 100
        
        # R-squared
        r2 = r2_score(actual_counts, predicted_counts)
        
        # Correlation coefficients
        pearson_r, _ = pearsonr(actual_counts, predicted_counts)
        spearman_r, _ = spearmanr(actual_counts, predicted_counts)
        
        return {
            'MAE': mae,
            'RMSE': rmse,
            'MAPE': mape,
            'R²': r2,
            'Pearson_r': pearson_r,
            'Spearman_r': spearman_r
        }
    
    def calculate_density_metrics(self) -> Dict[str, float]:
        """Calculate density estimation metrics"""
        
        pred_densities = []
        gt_densities = []
        
        for pred, gt in zip(self.predictions, self.ground_truths):
            if pred['density_map'] is not None and gt['density_map'] is not None:
                pred_densities.append(pred['density_map'])
                gt_densities.append(gt['density_map'])
        
        if not pred_densities:
            return {
                'Density_MSE': float('inf'),
                'Density_SSIM': 0.0,
                'Density_MAE': float('inf')
            }
        
        # Convert to tensors if needed
        if isinstance(pred_densities[0], np.ndarray):
            pred_densities = [torch.from_numpy(d) for d in pred_densities]
            gt_densities = [torch.from_numpy(d) for d in gt_densities]
        
        # Calculate MSE
        mse_scores = []
        mae_scores = []
        ssim_scores = []
        
        for pred_density, gt_density in zip(pred_densities, gt_densities):
            # MSE
            mse = F.mse_loss(pred_density, gt_density).item()
            mse_scores.append(mse)
            
            # MAE
            mae = F.l1_loss(pred_density, gt_density).item()
            mae_scores.append(mae)
            
            # SSIM (simplified version)
            ssim = self._calculate_ssim(pred_density, gt_density)
            ssim_scores.append(ssim)
        
        return {
            'Density_MSE': np.mean(mse_scores),
            'Density_SSIM': np.mean(ssim_scores),
            'Density_MAE': np.mean(mae_scores)
        }
    
    def _calculate_average_precision(self, 
                                   pred_boxes: List, 
                                   pred_scores: List, 
                                   gt_boxes: List, 
                                   iou_threshold: float) -> float:
        """Calculate Average Precision for given IoU threshold"""
        
        # Sort predictions by confidence
        sorted_indices = np.argsort(pred_scores)[::-1]
        sorted_pred_boxes = [pred_boxes[i] for i in sorted_indices]
        sorted_scores = [pred_scores[i] for i in sorted_indices]
        
        # Calculate precision and recall
        tp = np.zeros(len(sorted_pred_boxes))
        fp = np.zeros(len(sorted_pred_boxes))
        
        gt_matched = np.zeros(len(gt_boxes))
        
        for i, pred_box in enumerate(sorted_pred_boxes):
            # Find best matching ground truth
            best_iou = 0
            best_gt_idx = -1
            
            for j, gt_box in enumerate(gt_boxes):
                if gt_matched[j]:
                    continue
                
                iou = self._calculate_iou(pred_box, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = j
            
            if best_iou >= iou_threshold:
                tp[i] = 1
                gt_matched[best_gt_idx] = 1
            else:
                fp[i] = 1
        
        # Calculate cumulative precision and recall
        tp_cumsum = np.cumsum(tp)
        fp_cumsum = np.cumsum(fp)
        
        recalls = tp_cumsum / len(gt_boxes)
        precisions = tp_cumsum / (tp_cumsum + fp_cumsum + 1e-8)
        
        # Calculate AP using 11-point interpolation
        ap = 0
        for t in np.arange(0, 1.1, 0.1):
            if np.sum(recalls >= t) == 0:
                p = 0
            else:
                p = np.max(precisions[recalls >= t])
            ap += p / 11
        
        return ap
    
    def _calculate_precision_recall_f1(self, 
                                     pred_boxes: List, 
                                     pred_scores: List, 
                                     gt_boxes: List, 
                                     iou_threshold: float = 0.5,
                                     confidence_threshold: float = 0.5) -> Tuple[float, float, float]:
        """Calculate Precision, Recall, and F1-Score"""
        
        # Filter predictions by confidence
        filtered_pred_boxes = []
        for i, score in enumerate(pred_scores):
            if score >= confidence_threshold:
                filtered_pred_boxes.append(pred_boxes[i])
        
        if not filtered_pred_boxes:
            return 0.0, 0.0, 0.0
        
        # Calculate matches
        tp = 0
        gt_matched = np.zeros(len(gt_boxes))
        
        for pred_box in filtered_pred_boxes:
            best_iou = 0
            best_gt_idx = -1
            
            for j, gt_box in enumerate(gt_boxes):
                if gt_matched[j]:
                    continue
                
                iou = self._calculate_iou(pred_box, gt_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = j
            
            if best_iou >= iou_threshold:
                tp += 1
                gt_matched[best_gt_idx] = 1
        
        fp = len(filtered_pred_boxes) - tp
        fn = len(gt_boxes) - tp
        
        precision = tp / (tp + fp + 1e-8)
        recall = tp / (tp + fn + 1e-8)
        f1 = 2 * precision * recall / (precision + recall + 1e-8)
        
        return precision, recall, f1
    
    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """Calculate Intersection over Union (IoU) between two boxes"""
        
        # Convert to [x1, y1, x2, y2] format if needed
        if len(box1) == 4 and len(box2) == 4:
            x1_1, y1_1, x2_1, y2_1 = box1
            x1_2, y1_2, x2_2, y2_2 = box2
        else:
            return 0.0
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        
        # Calculate union
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_ssim(self, pred: torch.Tensor, gt: torch.Tensor) -> float:
        """Calculate Structural Similarity Index (SSIM)"""
        
        # Simplified SSIM calculation
        pred_np = pred.detach().cpu().numpy()
        gt_np = gt.detach().cpu().numpy()
        
        # Normalize to [0, 1]
        pred_np = (pred_np - pred_np.min()) / (pred_np.max() - pred_np.min() + 1e-8)
        gt_np = (gt_np - gt_np.min()) / (gt_np.max() - gt_np.min() + 1e-8)
        
        # Calculate means
        mu1 = np.mean(pred_np)
        mu2 = np.mean(gt_np)
        
        # Calculate variances and covariance
        sigma1_sq = np.var(pred_np)
        sigma2_sq = np.var(gt_np)
        sigma12 = np.mean((pred_np - mu1) * (gt_np - mu2))
        
        # SSIM constants
        c1 = 0.01 ** 2
        c2 = 0.03 ** 2
        
        # Calculate SSIM
        ssim = ((2 * mu1 * mu2 + c1) * (2 * sigma12 + c2)) / \
               ((mu1 ** 2 + mu2 ** 2 + c1) * (sigma1_sq + sigma2_sq + c2))
        
        return ssim
    
    def calculate_all_metrics(self) -> Dict[str, float]:
        """Calculate all metrics"""
        
        metrics = {}
        
        # Detection metrics
        detection_metrics = self.calculate_detection_metrics()
        metrics.update(detection_metrics)
        
        # Counting metrics
        counting_metrics = self.calculate_counting_metrics()
        metrics.update(counting_metrics)
        
        # Density metrics
        density_metrics = self.calculate_density_metrics()
        metrics.update(density_metrics)
        
        return metrics
    
    def generate_report(self, save_path: str = None) -> str:
        """Generate comprehensive evaluation report"""
        
        metrics = self.calculate_all_metrics()
        
        report = "FlowerCount-YOLO Evaluation Report\n"
        report += "=" * 50 + "\n\n"
        
        # Detection metrics
        report += "Detection Metrics:\n"
        report += "-" * 20 + "\n"
        report += f"mAP@0.5:      {metrics.get('mAP@0.5', 0):.4f}\n"
        report += f"mAP@0.5:0.95: {metrics.get('mAP@0.5:0.95', 0):.4f}\n"
        report += f"Precision:    {metrics.get('Precision', 0):.4f}\n"
        report += f"Recall:       {metrics.get('Recall', 0):.4f}\n"
        report += f"F1-Score:     {metrics.get('F1-Score', 0):.4f}\n\n"
        
        # Counting metrics
        report += "Counting Metrics:\n"
        report += "-" * 20 + "\n"
        report += f"MAE:          {metrics.get('MAE', 0):.4f}\n"
        report += f"RMSE:         {metrics.get('RMSE', 0):.4f}\n"
        report += f"MAPE:         {metrics.get('MAPE', 0):.4f}%\n"
        report += f"R²:           {metrics.get('R²', 0):.4f}\n"
        report += f"Pearson r:    {metrics.get('Pearson_r', 0):.4f}\n"
        report += f"Spearman r:   {metrics.get('Spearman_r', 0):.4f}\n\n"
        
        # Density metrics
        report += "Density Estimation Metrics:\n"
        report += "-" * 30 + "\n"
        report += f"MSE:          {metrics.get('Density_MSE', 0):.4f}\n"
        report += f"MAE:          {metrics.get('Density_MAE', 0):.4f}\n"
        report += f"SSIM:         {metrics.get('Density_SSIM', 0):.4f}\n"
        
        if save_path:
            with open(save_path, 'w') as f:
                f.write(report)
        
        return report
    
    def plot_metrics(self, save_dir: str = None):
        """Generate metric visualization plots"""
        
        if save_dir:
            save_dir = Path(save_dir)
            save_dir.mkdir(parents=True, exist_ok=True)
        
        # Plot counting accuracy
        self._plot_counting_accuracy(save_dir)
        
        # Plot detection performance
        self._plot_detection_performance(save_dir)
        
        # Plot correlation analysis
        self._plot_correlation_analysis(save_dir)
    
    def _plot_counting_accuracy(self, save_dir: Path = None):
        """Plot counting accuracy visualization"""
        
        predicted_counts = [pred['predicted_count'] for pred in self.predictions]
        actual_counts = [gt['actual_count'] for gt in self.ground_truths]
        
        plt.figure(figsize=(10, 8))
        
        # Scatter plot
        plt.subplot(2, 2, 1)
        plt.scatter(actual_counts, predicted_counts, alpha=0.6)
        plt.plot([0, max(actual_counts)], [0, max(actual_counts)], 'r--', label='Perfect Prediction')
        plt.xlabel('Actual Count')
        plt.ylabel('Predicted Count')
        plt.title('Counting Accuracy')
        plt.legend()
        plt.grid(True)
        
        # Error distribution
        plt.subplot(2, 2, 2)
        errors = np.array(predicted_counts) - np.array(actual_counts)
        plt.hist(errors, bins=20, alpha=0.7)
        plt.xlabel('Prediction Error')
        plt.ylabel('Frequency')
        plt.title('Error Distribution')
        plt.grid(True)
        
        # Absolute error vs count
        plt.subplot(2, 2, 3)
        abs_errors = np.abs(errors)
        plt.scatter(actual_counts, abs_errors, alpha=0.6)
        plt.xlabel('Actual Count')
        plt.ylabel('Absolute Error')
        plt.title('Absolute Error vs Count')
        plt.grid(True)
        
        # Relative error vs count
        plt.subplot(2, 2, 4)
        rel_errors = abs_errors / (np.array(actual_counts) + 1e-8) * 100
        plt.scatter(actual_counts, rel_errors, alpha=0.6)
        plt.xlabel('Actual Count')
        plt.ylabel('Relative Error (%)')
        plt.title('Relative Error vs Count')
        plt.grid(True)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'counting_accuracy.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def _plot_detection_performance(self, save_dir: Path = None):
        """Plot detection performance metrics"""
        
        metrics = self.calculate_detection_metrics()
        
        # Create bar plot of detection metrics
        plt.figure(figsize=(10, 6))
        
        metric_names = ['mAP@0.5', 'mAP@0.5:0.95', 'Precision', 'Recall', 'F1-Score']
        metric_values = [metrics.get(name, 0) for name in metric_names]
        
        bars = plt.bar(metric_names, metric_values, color=['skyblue', 'lightcoral', 'lightgreen', 'gold', 'plum'])
        
        # Add value labels on bars
        for bar, value in zip(bars, metric_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{value:.3f}', ha='center', va='bottom')
        
        plt.ylabel('Score')
        plt.title('Detection Performance Metrics')
        plt.ylim(0, 1.1)
        plt.grid(True, alpha=0.3)
        
        if save_dir:
            plt.savefig(save_dir / 'detection_performance.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def _plot_correlation_analysis(self, save_dir: Path = None):
        """Plot correlation analysis"""
        
        predicted_counts = [pred['predicted_count'] for pred in self.predictions]
        actual_counts = [gt['actual_count'] for gt in self.ground_truths]
        
        # Calculate correlation
        pearson_r, _ = pearsonr(actual_counts, predicted_counts)
        spearman_r, _ = spearmanr(actual_counts, predicted_counts)
        
        plt.figure(figsize=(12, 5))
        
        # Pearson correlation
        plt.subplot(1, 2, 1)
        plt.scatter(actual_counts, predicted_counts, alpha=0.6)
        z = np.polyfit(actual_counts, predicted_counts, 1)
        p = np.poly1d(z)
        plt.plot(actual_counts, p(actual_counts), "r--", alpha=0.8)
        plt.xlabel('Actual Count')
        plt.ylabel('Predicted Count')
        plt.title(f'Pearson Correlation (r = {pearson_r:.3f})')
        plt.grid(True)
        
        # Spearman correlation (rank-based)
        plt.subplot(1, 2, 2)
        from scipy.stats import rankdata
        actual_ranks = rankdata(actual_counts)
        predicted_ranks = rankdata(predicted_counts)
        plt.scatter(actual_ranks, predicted_ranks, alpha=0.6)
        z = np.polyfit(actual_ranks, predicted_ranks, 1)
        p = np.poly1d(z)
        plt.plot(actual_ranks, p(actual_ranks), "r--", alpha=0.8)
        plt.xlabel('Actual Count Rank')
        plt.ylabel('Predicted Count Rank')
        plt.title(f'Spearman Correlation (ρ = {spearman_r:.3f})')
        plt.grid(True)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'correlation_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()


def evaluate_model(model, dataloader, device='cuda', save_dir=None):
    """
    Evaluate FlowerCount-YOLO model
    
    Args:
        model: Trained model
        dataloader: Validation/test dataloader
        device: Computation device
        save_dir: Directory to save results
        
    Returns:
        Dictionary of evaluation metrics
    """
    
    metrics_calculator = FlowerCountingMetrics()
    model.eval()
    
    with torch.no_grad():
        for batch in dataloader:
            # Move batch to device
            images = batch['images'].to(device)
            
            # Get predictions
            predictions = model(images)
            
            # Add to metrics calculator
            metrics_calculator.add_batch(predictions, batch, batch.get('image_ids'))
    
    # Calculate all metrics
    metrics = metrics_calculator.calculate_all_metrics()
    
    # Generate report
    report = metrics_calculator.generate_report(
        save_path=save_dir / 'evaluation_report.txt' if save_dir else None
    )
    
    # Generate plots
    if save_dir:
        metrics_calculator.plot_metrics(save_dir)
    
    print(report)
    
    return metrics


if __name__ == "__main__":
    # Example usage
    metrics = FlowerCountingMetrics()
    
    # Add some dummy data for testing
    dummy_predictions = {
        'boxes': [[[10, 10, 50, 50], [60, 60, 100, 100]]],
        'scores': [[0.9, 0.8]],
        'labels': [[1, 1]],
        'predicted_count': [2]
    }
    
    dummy_ground_truths = {
        'boxes': [[[15, 15, 55, 55], [65, 65, 105, 105]]],
        'labels': [[1, 1]],
        'actual_count': [2]
    }
    
    metrics.add_batch(dummy_predictions, dummy_ground_truths, ['test_image'])
    
    # Calculate metrics
    results = metrics.calculate_all_metrics()
    print("Evaluation Results:")
    for metric, value in results.items():
        print(f"{metric}: {value:.4f}")
    
    # Generate report
    report = metrics.generate_report()
    print("\n" + report)
