@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                FlowerCount-YOLO CUDA版 超速安装                             ║
echo ║                        (国内镜像源加速)                                     ║
echo ║                                                                              ║
echo ║    🌸 Advanced Flower Detection and Counting System                         ║
echo ║    🔥 CUDA GPU加速 + 国内镜像源 = 极速安装                                  ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限确认
) else (
    echo ❌ 需要管理员权限！请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 🔍 检查NVIDIA GPU和驱动...
nvidia-smi >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo ✅ NVIDIA GPU检测成功
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits
) else (
    echo ⚠️  未检测到NVIDIA GPU或驱动，将安装CPU版本
    set USE_CPU=1
)

echo.
echo 🔍 检查conda...
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到conda！请先安装Anaconda
    pause
    exit /b 1
)
echo ✅ conda已安装

echo.
echo 🏗️ 创建CUDA环境...
conda create -n flowercount-yolo-cuda python=3.9 -y
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 环境创建失败！
    pause
    exit /b 1
)

echo.
echo 🎯 激活环境...
call conda activate flowercount-yolo-cuda

echo.
echo 🚀 配置超速镜像源...
echo 配置清华大学镜像源（国内最快）...
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn
pip config set global.timeout 120
echo ✅ 镜像源配置完成

echo.
echo 📦 升级pip...
python -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple/

echo.
if defined USE_CPU (
    echo 💻 安装PyTorch CPU版本...
    pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/
) else (
    echo 🔥 安装PyTorch CUDA版本...
    echo 正在下载CUDA版本，请稍候...
    
    REM 尝试多个CUDA版本和镜像源
    echo 尝试CUDA 11.8版本（推荐）...
    pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  清华镜像失败，尝试官方CUDA源...
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
        if %ERRORLEVEL% NEQ 0 (
            echo ⚠️  CUDA 11.8失败，尝试CUDA 12.1...
            pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
            if %ERRORLEVEL% NEQ 0 (
                echo ⚠️  所有CUDA版本失败，安装CPU版本...
                pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/
            )
        )
    )
)

echo.
echo 🧪 验证PyTorch安装...
python -c "import torch; print('✅ PyTorch版本:', torch.__version__); cuda_available = torch.cuda.is_available(); print('🔥 CUDA可用:', cuda_available); print('🎮 GPU数量:', torch.cuda.device_count() if cuda_available else 0); print('💾 GPU名称:', torch.cuda.get_device_name(0) if cuda_available else 'CPU模式')"

echo.
echo 📚 批量安装核心包（超速下载）...
echo 正在安装核心AI/ML包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ ultralytics opencv-python opencv-contrib-python numpy scipy pandas matplotlib seaborn scikit-learn
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  清华镜像失败，尝试阿里云镜像...
    pip install -i https://mirrors.aliyun.com/pypi/simple/ ultralytics opencv-python opencv-contrib-python numpy scipy pandas matplotlib seaborn scikit-learn
)

echo.
echo 🎨 安装计算机视觉包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ albumentations scikit-image pillow tqdm pyyaml

echo.
echo 📊 安装实验跟踪包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ wandb tensorboard mlflow

echo.
echo 🔍 安装可解释性包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ grad-cam transformers timm

echo.
echo 📏 安装评估和工具包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pycocotools torchmetrics rich hydra-core

echo.
echo 🧪 最终验证和性能测试...
python -c "
print('🔍 FlowerCount-YOLO环境验证...')
import sys
print('🐍 Python版本:', sys.version.split()[0])

# 导入测试
packages = ['torch', 'torchvision', 'cv2', 'numpy', 'matplotlib', 'pandas', 'sklearn', 'ultralytics']
failed = []
for pkg in packages:
    try:
        __import__(pkg)
        print(f'✅ {pkg}')
    except ImportError:
        print(f'❌ {pkg}')
        failed.append(pkg)

if failed:
    print(f'⚠️  失败的包: {failed}')
else:
    print('✅ 所有核心包导入成功!')

# CUDA测试
import torch
cuda_available = torch.cuda.is_available()
print(f'🔥 CUDA状态: {cuda_available}')

if cuda_available:
    print(f'🎮 GPU: {torch.cuda.get_device_name(0)}')
    memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
    print(f'💾 GPU内存: {memory_gb:.1f}GB')
    
    # CUDA性能测试
    print('🧪 CUDA性能测试...')
    import time
    x = torch.randn(2000, 2000).cuda()
    y = torch.randn(2000, 2000).cuda()
    
    start_time = time.time()
    z = torch.mm(x, y)
    torch.cuda.synchronize()
    end_time = time.time()
    
    print(f'⚡ CUDA计算时间: {(end_time-start_time)*1000:.1f}ms')
    print('✅ CUDA功能正常!')
else:
    print('💻 CPU模式 - 建议检查CUDA安装')

print('🎯 FlowerCount-YOLO环境准备完毕!')
"

echo.
echo 🎉 CUDA版安装完成！
echo.
echo 📋 安装总结:
echo    ✅ 环境: flowercount-yolo-cuda
echo    ✅ Python: 3.9
echo    ✅ PyTorch: CUDA版本
echo    ✅ 镜像源: 清华大学（永久加速）
echo    ✅ 所有依赖包已安装
echo.
echo 🚀 立即开始使用:
echo    1. python quick_start.py --mode demo
echo    2. python start_project.py
echo    3. python run_complete_experiment.py
echo.
echo 🔥 GPU加速训练已就绪！
echo 🌸 开始您的花朵检测研究之旅吧！
echo.
pause
