#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练脚本 - 验证训练环境
"""

import os
import sys
import time
from pathlib import Path
import torch
from ultralytics import YOL<PERSON>

def test_environment():
    """测试环境"""
    print("🔍 测试训练环境...")
    
    # 检查PyTorch
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
        print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 检查ultralytics
    try:
        from ultralytics import __version__
        print(f"   Ultralytics版本: {__version__}")
    except:
        print("   Ultralytics版本: 未知")
    
    return True

def test_model_creation():
    """测试模型创建"""
    print("\n🎯 测试模型创建...")
    
    try:
        model = YOLO('yolov8n.pt')
        print("   ✅ YOLO模型创建成功")
        return model
    except Exception as e:
        print(f"   ❌ 模型创建失败: {e}")
        return None

def test_dataset():
    """测试数据集"""
    print("\n📊 测试数据集...")
    
    dataset_config = Path("dataset/dataset.yaml")
    if not dataset_config.exists():
        print("   ❌ 数据集配置文件不存在")
        return False
    
    print(f"   ✅ 数据集配置存在: {dataset_config}")
    
    # 检查图像目录
    train_dir = Path("dataset/train/images")
    val_dir = Path("dataset/val/images")
    
    if train_dir.exists():
        train_images = list(train_dir.glob("*.jpg"))
        print(f"   ✅ 训练图像: {len(train_images)} 张")
    else:
        print("   ❌ 训练目录不存在")
        return False
    
    if val_dir.exists():
        val_images = list(val_dir.glob("*.jpg"))
        print(f"   ✅ 验证图像: {len(val_images)} 张")
    else:
        print("   ❌ 验证目录不存在")
        return False
    
    return len(train_images) > 0 and len(val_images) > 0

def test_short_training(model):
    """测试短时间训练"""
    print("\n🚀 测试短时间训练（1个epoch）...")
    
    try:
        # 非常短的训练测试
        results = model.train(
            data="dataset/dataset.yaml",
            epochs=1,
            imgsz=640,
            batch=1,
            device='auto',
            workers=1,
            patience=1,
            save=True,
            val=False,  # 跳过验证以节省时间
            plots=False,  # 跳过图表生成
            verbose=True,
            project='runs/train',
            name='test_training',
            exist_ok=True
        )
        
        print("   ✅ 短时间训练测试成功")
        print(f"   📁 结果保存在: {results.save_dir}")
        return True
        
    except Exception as e:
        print(f"   ❌ 短时间训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🌸 FlowerCount-YOLO 训练环境测试")
    print("=" * 50)
    
    # 测试环境
    if not test_environment():
        return False
    
    # 测试数据集
    if not test_dataset():
        return False
    
    # 测试模型创建
    model = test_model_creation()
    if model is None:
        return False
    
    # 测试短时间训练
    if not test_short_training(model):
        return False
    
    print("\n🎉 所有测试通过！")
    print("   环境配置正确，可以进行完整训练")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 测试完成 - 环境正常")
        print("可以运行完整的训练流程")
    else:
        print("\n❌ 测试失败 - 环境有问题")
        print("请检查错误信息并修复问题")
