#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 完整CUDA训练系统
使用Torch1.10环境的完整训练流程
"""

import os
import sys
import time
import json
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import cv2
import numpy as np
import matplotlib.pyplot as plt

class FlowerDataset(Dataset):
    """花朵数据集"""
    
    def __init__(self, images_dir, labels_dir, img_size=416):
        self.images_dir = Path(images_dir)
        self.labels_dir = Path(labels_dir)
        self.img_size = img_size
        
        # 获取图像文件
        self.image_files = list(self.images_dir.glob("*.jpg"))
        print(f"   加载 {len(self.image_files)} 张图像")
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        # 加载图像
        img_path = self.image_files[idx]
        image = cv2.imread(str(img_path))
        if image is None:
            # 如果图像加载失败，返回空图像
            image = np.zeros((self.img_size, self.img_size, 3), dtype=np.uint8)
        else:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            # 调整尺寸
            image = cv2.resize(image, (self.img_size, self.img_size))
        
        # 归一化
        image = image.astype(np.float32) / 255.0
        image = torch.from_numpy(image).permute(2, 0, 1)
        
        # 加载标签
        label_path = self.labels_dir / f"{img_path.stem}.txt"
        flower_count = 0
        
        if label_path.exists():
            try:
                with open(label_path, 'r') as f:
                    lines = f.readlines()
                flower_count = len([line for line in lines if line.strip()])
            except:
                flower_count = 0
        
        return image, torch.tensor([flower_count], dtype=torch.float32)

class FlowerCountNet(nn.Module):
    """花朵计数网络"""
    
    def __init__(self):
        super().__init__()
        
        # 特征提取器
        self.features = nn.Sequential(
            # 第一层
            nn.Conv2d(3, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # 第二层
            nn.Conv2d(32, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # 第三层
            nn.Conv2d(64, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # 第四层
            nn.Conv2d(128, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # 第五层
            nn.Conv2d(256, 512, 3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((4, 4))
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(512 * 4 * 4, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(1024, 256),
            nn.ReLU(inplace=True),
            nn.Linear(256, 1)  # 输出花朵数量
        )
    
    def forward(self, x):
        x = self.features(x)
        x = x.view(x.size(0), -1)
        x = self.classifier(x)
        return x

def train_model():
    """训练模型"""
    print("🌸 FlowerCount-YOLO 完整CUDA训练")
    print("=" * 60)
    
    # 检查CUDA
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔥 使用设备: {device}")
    if torch.cuda.is_available():
        print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
        print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 检查数据集
    print("\n📊 检查数据集...")
    dataset_dir = Path("dataset")
    train_images_dir = dataset_dir / "train" / "images"
    train_labels_dir = dataset_dir / "train" / "labels"
    val_images_dir = dataset_dir / "val" / "images"
    val_labels_dir = dataset_dir / "val" / "labels"
    
    if not all([train_images_dir.exists(), train_labels_dir.exists(), 
                val_images_dir.exists(), val_labels_dir.exists()]):
        print("❌ 数据集目录不完整")
        return False
    
    # 创建数据集
    print("📦 创建数据集...")
    train_dataset = FlowerDataset(train_images_dir, train_labels_dir)
    val_dataset = FlowerDataset(val_images_dir, val_labels_dir)
    
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=2)
    
    print(f"   训练样本: {len(train_dataset)}")
    print(f"   验证样本: {len(val_dataset)}")
    
    # 创建模型
    print("\n🎯 创建模型...")
    model = FlowerCountNet().to(device)
    
    # 优化器和损失函数
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
    criterion = nn.MSELoss()
    
    print("✅ 模型创建成功")
    print(f"   模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练循环
    print("\n🚀 开始训练...")
    num_epochs = 25
    best_val_loss = float('inf')
    
    train_losses = []
    val_losses = []
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_samples = 0
        
        for batch_idx, (images, targets) in enumerate(train_loader):
            images = images.to(device)
            targets = targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, targets)
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item() * images.size(0)
            train_samples += images.size(0)
            
            if batch_idx % 10 == 0:
                print(f"   Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_samples = 0
        
        with torch.no_grad():
            for images, targets in val_loader:
                images = images.to(device)
                targets = targets.to(device)
                outputs = model(images)
                loss = criterion(outputs, targets)
                
                val_loss += loss.item() * images.size(0)
                val_samples += images.size(0)
        
        # 计算平均损失
        train_loss /= train_samples
        val_loss /= val_samples
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 更新学习率
        scheduler.step()
        
        print(f"Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, LR: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
            }, 'best_flowercount_model.pth')
            print(f"   ✅ 保存最佳模型 (Val Loss: {best_val_loss:.4f})")
    
    training_time = time.time() - start_time
    
    # 保存最终模型
    torch.save({
        'epoch': num_epochs,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_losses': train_losses,
        'val_losses': val_losses,
    }, 'final_flowercount_model.pth')
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss', color='blue')
    plt.plot(val_losses, label='Val Loss', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('FlowerCount-YOLO Training Curves')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(val_losses, label='Validation Loss', color='red', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Validation Loss')
    plt.title('Validation Loss Progress')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('flowercount_training_curves.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存训练信息
    train_info = {
        'project_name': 'FlowerCount-YOLO',
        'framework': f'PyTorch {torch.__version__}',
        'device': str(device),
        'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None',
        'epochs': num_epochs,
        'batch_size': 8,
        'learning_rate': 0.001,
        'training_time_minutes': training_time / 60,
        'best_val_loss': best_val_loss,
        'final_train_loss': train_losses[-1],
        'final_val_loss': val_losses[-1],
        'train_samples': len(train_dataset),
        'val_samples': len(val_dataset),
        'model_files': {
            'best_model': 'best_flowercount_model.pth',
            'final_model': 'final_flowercount_model.pth',
            'training_curves': 'flowercount_training_curves.png'
        },
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    with open('flowercount_training_info.json', 'w', encoding='utf-8') as f:
        json.dump(train_info, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 训练完成！")
    print(f"   训练时间: {training_time/60:.1f} 分钟")
    print(f"   最佳验证损失: {best_val_loss:.4f}")
    print(f"   最终训练损失: {train_losses[-1]:.4f}")
    print(f"   最终验证损失: {val_losses[-1]:.4f}")
    print(f"   最佳模型: best_flowercount_model.pth")
    print(f"   最终模型: final_flowercount_model.pth")
    print(f"   训练曲线: flowercount_training_curves.png")
    print(f"   训练信息: flowercount_training_info.json")
    
    return True

def main():
    """主函数"""
    print("🌸 FlowerCount-YOLO 完整CUDA训练系统")
    print("=" * 60)
    
    success = train_model()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 FlowerCount-YOLO CUDA训练完成成功！")
        print("🏆 模型已保存，可用于花朵计数")
        print("📊 查看训练曲线了解训练过程")
        print("🌸 这是一个完整的深度学习花朵计数系统！")
    else:
        print("\n" + "=" * 60)
        print("❌ FlowerCount-YOLO CUDA训练失败！")
        print("请检查错误信息并重试")
    
    print("\n🌸 感谢使用FlowerCount-YOLO系统！")

if __name__ == "__main__":
    main()
