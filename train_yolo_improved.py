#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 改进训练脚本
使用更智能的标注生成和训练策略
"""

import os
import sys
import json
import random
import shutil
from pathlib import Path
import cv2
import numpy as np
from ultralytics import YOLO
import yaml
from PIL import Image, ImageDraw
import torch

def print_header():
    """打印程序头部信息"""
    print("╔" + "═" * 70 + "╗")
    print("║" + " " * 20 + "FlowerCount-YOLO 改进训练" + " " * 20 + "║")
    print("║" + " " * 70 + "║")
    print("║" + " " * 8 + "🌸 使用智能标注生成进行花朵检测训练" + " " * 8 + "║")
    print("║" + " " * 12 + "💻 CPU/GPU自适应训练" + " " * 21 + "║")
    print("║" + " " * 70 + "║")
    print("╚" + "═" * 70 + "╝")
    print()

def check_environment():
    """检查训练环境"""
    print("🔍 训练环境检查:")
    print("=" * 20)
    
    # 检查conda环境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'Unknown')
    print(f"   📦 Conda环境: {conda_env}")
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"   🐍 Python: {python_version}")
    
    # 检查PyTorch
    try:
        import torch
        print(f"   🔥 PyTorch: {torch.__version__}")
        cuda_available = torch.cuda.is_available()
        print(f"   🎮 CUDA可用: {cuda_available}")
        if cuda_available:
            print(f"   🚀 使用GPU训练")
            device = 'cuda'
        else:
            print(f"   💻 使用CPU训练")
            device = 'cpu'
    except ImportError:
        print("   ❌ PyTorch未安装")
        device = 'cpu'
    
    print()
    return device

def prepare_dataset():
    """准备数据集"""
    print("📁 准备数据集:")
    print("=" * 15)
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("   ❌ data目录不存在")
        return False
    
    # 查找图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = []
    for ext in image_extensions:
        image_files.extend(list(data_dir.glob(f"*{ext}")))
        image_files.extend(list(data_dir.glob(f"*{ext.upper()}")))
    
    if not image_files:
        print("   ❌ 未找到图像文件")
        return False
    
    print(f"   ✅ 找到 {len(image_files)} 张图像")
    
    # 创建数据集配置
    dataset_config = {
        'path': str(Path.cwd()),
        'train': 'data',
        'val': 'data',
        'nc': 1,
        'names': ['flower']
    }
    
    with open('dataset.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(dataset_config, f, default_flow_style=False)
    
    print(f"   ✅ 数据集配置: dataset.yaml")
    print()
    return True, image_files

def detect_flowers_with_opencv(image_path):
    """使用OpenCV检测花朵区域"""
    try:
        # 读取图像
        img = cv2.imread(str(image_path))
        if img is None:
            return []
        
        height, width = img.shape[:2]
        
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 定义花朵颜色范围（红色、粉色、黄色、紫色、白色）
        color_ranges = [
            # 红色花朵
            ([0, 50, 50], [10, 255, 255]),
            ([170, 50, 50], [180, 255, 255]),
            # 粉色花朵
            ([140, 50, 50], [170, 255, 255]),
            # 黄色花朵
            ([15, 50, 50], [35, 255, 255]),
            # 紫色花朵
            ([120, 50, 50], [140, 255, 255]),
            # 白色花朵
            ([0, 0, 200], [180, 30, 255]),
        ]
        
        # 合并所有颜色掩码
        combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
        for lower, upper in color_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 过滤轮廓并生成边界框
        flowers = []
        min_area = 800  # 最小面积
        max_area = width * height * 0.25  # 最大面积
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area < area < max_area:
                # 计算轮廓的紧凑度
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    compactness = 4 * np.pi * area / (perimeter * perimeter)
                    if compactness > 0.3:  # 过滤掉太不规则的形状
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        # 转换为YOLO格式 (中心点坐标和相对尺寸)
                        center_x = (x + w/2) / width
                        center_y = (y + h/2) / height
                        rel_width = w / width
                        rel_height = h / height
                        
                        # 确保坐标在有效范围内
                        if 0 < center_x < 1 and 0 < center_y < 1 and rel_width > 0 and rel_height > 0:
                            flowers.append([0, center_x, center_y, rel_width, rel_height])
        
        return flowers
        
    except Exception as e:
        print(f"   ⚠️  检测失败 {image_path.name}: {e}")
        return []

def create_smart_annotations(image_files):
    """创建智能标注"""
    print("🏷️  创建智能标注:")
    print("=" * 20)
    
    labels_dir = Path("data/labels")
    labels_dir.mkdir(exist_ok=True)
    
    total_flowers = 0
    annotated_images = 0
    
    for image_file in image_files:
        # 使用OpenCV检测花朵
        flowers = detect_flowers_with_opencv(image_file)
        
        if flowers:
            # 创建标注文件
            label_file = labels_dir / f"{image_file.stem}.txt"
            with open(label_file, 'w') as f:
                for flower in flowers:
                    f.write(f"{flower[0]} {flower[1]:.6f} {flower[2]:.6f} {flower[3]:.6f} {flower[4]:.6f}\n")
            
            total_flowers += len(flowers)
            annotated_images += 1
            print(f"   ✅ {image_file.name}: 检测到 {len(flowers)} 朵花")
        else:
            # 创建空标注文件
            label_file = labels_dir / f"{image_file.stem}.txt"
            with open(label_file, 'w') as f:
                pass
            print(f"   ⚠️  {image_file.name}: 未检测到花朵")
    
    print(f"   📊 总计: {annotated_images}/{len(image_files)} 张图像有标注")
    print(f"   🌸 检测到 {total_flowers} 朵花")
    print(f"   📁 标注目录: {labels_dir}")
    print()
    
    return total_flowers > 0
