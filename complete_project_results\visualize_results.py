#!/usr/bin/env python3
# FlowerCount-YOLO 结果可视化脚本
import json
import matplotlib.pyplot as plt

def plot_results():
    with open('complete_results.json', 'r') as f:
        results = json.load(f)
    
    # 绘制消融实验结果
    ablation = results['ablation_results']
    methods = list(ablation.keys())
    maps = [ablation[m]['mAP@0.5'] for m in methods]
    
    plt.figure(figsize=(10, 6))
    plt.bar(methods, maps)
    plt.title('Ablation Study Results')
    plt.ylabel('mAP@0.5')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('ablation_results.png', dpi=300)
    plt.show()

if __name__ == '__main__':
    plot_results()
