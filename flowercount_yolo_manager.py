#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 项目管理器
统一管理数据处理、模型训练、评估和部署的完整流程
"""

import os
import sys
import json
import time
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
import yaml
import logging

class FlowerCountYOLOManager:
    """FlowerCount-YOLO项目管理器"""
    
    def __init__(self, config_path: str = "flowercount_yolo_config.yaml"):
        """
        初始化项目管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.project_root = Path.cwd()
        
        # 加载配置
        self.config = self.load_config()
        
        # 设置日志
        self.setup_logging()
        
        # 项目状态
        self.status = {
            'annotation_completed': False,
            'dataset_split_completed': False,
            'training_completed': False,
            'evaluation_completed': False,
            'deployment_ready': False
        }
        
        self.logger.info("🚀 FlowerCount-YOLO Manager 初始化完成")
        self.logger.info(f"📁 项目根目录: {self.project_root}")
        self.logger.info(f"⚙️  配置文件: {self.config_path}")
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        return config
    
    def setup_logging(self):
        """设置日志系统"""
        log_dir = Path(self.config.get('experiment', {}).get('logging', {}).get('save_dir', 'logs'))
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"flowercount_yolo_{time.strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('FlowerCountYOLO')
    
    def check_dependencies(self) -> bool:
        """检查项目依赖"""
        self.logger.info("🔍 检查项目依赖...")
        
        required_packages = [
            'torch', 'torchvision', 'ultralytics', 'opencv-python',
            'numpy', 'matplotlib', 'pyyaml', 'tqdm'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                self.logger.info(f"   ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                self.logger.error(f"   ❌ {package}")
        
        if missing_packages:
            self.logger.error(f"缺少依赖包: {missing_packages}")
            return False
        
        self.logger.info("✅ 所有依赖检查通过")
        return True
    
    def run_annotation(self) -> bool:
        """运行自动标注"""
        self.logger.info("🏷️  开始自动标注...")
        
        try:
            # 检查是否已有标注
            labels_dir = Path("data/labels")
            if labels_dir.exists() and list(labels_dir.glob("*.txt")):
                self.logger.info("   📋 发现现有标注文件")
                response = input("是否重新生成标注？(y/N): ")
                if response.lower() != 'y':
                    self.status['annotation_completed'] = True
                    return True
            
            # 运行自动标注
            result = subprocess.run([
                sys.executable, "high_density_annotator.py"
            ], capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                self.logger.info("✅ 自动标注完成")
                self.status['annotation_completed'] = True
                return True
            else:
                self.logger.error(f"❌ 自动标注失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 自动标注过程中出现错误: {e}")
            return False
    
    def run_dataset_split(self) -> bool:
        """运行数据集分割"""
        self.logger.info("📊 开始数据集分割...")
        
        try:
            # 检查是否已有分割数据集
            dataset_dir = Path("dataset")
            if dataset_dir.exists() and (dataset_dir / "train").exists():
                self.logger.info("   📁 发现现有数据集分割")
                response = input("是否重新分割数据集？(y/N): ")
                if response.lower() != 'y':
                    self.status['dataset_split_completed'] = True
                    return True
            
            # 运行数据集分割
            result = subprocess.run([
                sys.executable, "create_dataset_split.py"
            ], capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                self.logger.info("✅ 数据集分割完成")
                self.status['dataset_split_completed'] = True
                return True
            else:
                self.logger.error(f"❌ 数据集分割失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 数据集分割过程中出现错误: {e}")
            return False
    
    def run_training(self, stage: str = "all") -> bool:
        """运行模型训练"""
        self.logger.info(f"🎯 开始模型训练 (阶段: {stage})...")
        
        try:
            # 检查数据集
            dataset_config = Path("dataset/dataset.yaml")
            if not dataset_config.exists():
                self.logger.error("❌ 数据集配置文件不存在，请先运行数据集分割")
                return False
            
            # 运行训练
            if stage == "all":
                result = subprocess.run([
                    sys.executable, "train_flowercount_yolo.py"
                ], capture_output=False, text=True)
            else:
                # 运行特定阶段的训练
                result = subprocess.run([
                    sys.executable, "train_flowercount_yolo.py", "--stage", stage
                ], capture_output=False, text=True)
            
            if result.returncode == 0:
                self.logger.info("✅ 模型训练完成")
                self.status['training_completed'] = True
                return True
            else:
                self.logger.error("❌ 模型训练失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 模型训练过程中出现错误: {e}")
            return False
    
    def run_evaluation(self) -> bool:
        """运行模型评估"""
        self.logger.info("📈 开始模型评估...")
        
        try:
            # 查找最佳模型
            best_model_paths = list(Path("runs/train").glob("*/weights/best.pt"))
            if not best_model_paths:
                self.logger.error("❌ 未找到训练好的模型")
                return False
            
            # 使用最新的模型
            best_model = sorted(best_model_paths, key=lambda x: x.stat().st_mtime)[-1]
            self.logger.info(f"   🎯 使用模型: {best_model}")
            
            # 运行评估
            from ultralytics import YOLO
            model = YOLO(str(best_model))
            
            # 验证模型
            val_results = model.val(
                data="dataset/dataset.yaml",
                imgsz=self.config['val']['imgsz'],
                batch=self.config['val']['batch_size'],
                conf=self.config['val']['conf'],
                iou=self.config['val']['iou'],
                max_det=self.config['val']['max_det'],
                save_json=True,
                plots=True
            )
            
            # 保存评估结果
            eval_results = {
                'model_path': str(best_model),
                'mAP50': float(val_results.box.map50) if hasattr(val_results.box, 'map50') else 0.0,
                'mAP50-95': float(val_results.box.map) if hasattr(val_results.box, 'map') else 0.0,
                'precision': float(val_results.box.mp) if hasattr(val_results.box, 'mp') else 0.0,
                'recall': float(val_results.box.mr) if hasattr(val_results.box, 'mr') else 0.0,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            eval_file = Path("evaluation_results.json")
            with open(eval_file, 'w', encoding='utf-8') as f:
                json.dump(eval_results, f, indent=2, ensure_ascii=False)
            
            self.logger.info("✅ 模型评估完成")
            self.logger.info(f"   📊 mAP@0.5: {eval_results['mAP50']:.3f}")
            self.logger.info(f"   📊 mAP@0.5:0.95: {eval_results['mAP50-95']:.3f}")
            self.logger.info(f"   📁 结果保存: {eval_file}")
            
            self.status['evaluation_completed'] = True
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 模型评估过程中出现错误: {e}")
            return False
    
    def generate_report(self) -> bool:
        """生成项目报告"""
        self.logger.info("📋 生成项目报告...")
        
        try:
            report = {
                'project_info': {
                    'name': self.config['experiment']['name'],
                    'version': self.config['experiment']['version'],
                    'description': self.config['experiment']['description'],
                    'author': self.config['experiment']['author'],
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                },
                'status': self.status,
                'config': self.config,
                'file_structure': self.get_file_structure()
            }
            
            # 添加评估结果
            eval_file = Path("evaluation_results.json")
            if eval_file.exists():
                with open(eval_file, 'r', encoding='utf-8') as f:
                    report['evaluation_results'] = json.load(f)
            
            # 保存报告
            report_file = Path(f"project_report_{time.strftime('%Y%m%d_%H%M%S')}.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"✅ 项目报告生成完成: {report_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 生成项目报告时出现错误: {e}")
            return False
    
    def get_file_structure(self) -> Dict:
        """获取项目文件结构"""
        structure = {}
        
        important_paths = [
            "data", "dataset", "runs", "logs", "annotation_visualizations",
            "src", "configs", "scripts"
        ]
        
        for path_name in important_paths:
            path = Path(path_name)
            if path.exists():
                if path.is_dir():
                    structure[path_name] = {
                        'type': 'directory',
                        'files': len(list(path.rglob('*'))) if path.exists() else 0
                    }
                else:
                    structure[path_name] = {
                        'type': 'file',
                        'size': path.stat().st_size if path.exists() else 0
                    }
        
        return structure
    
    def run_full_pipeline(self) -> bool:
        """运行完整的项目流程"""
        self.logger.info("🚀 开始完整的FlowerCount-YOLO项目流程")
        self.logger.info("=" * 60)
        
        # 1. 检查依赖
        if not self.check_dependencies():
            return False
        
        # 2. 自动标注
        if not self.status['annotation_completed']:
            if not self.run_annotation():
                return False
        
        # 3. 数据集分割
        if not self.status['dataset_split_completed']:
            if not self.run_dataset_split():
                return False
        
        # 4. 模型训练
        if not self.status['training_completed']:
            if not self.run_training():
                return False
        
        # 5. 模型评估
        if not self.status['evaluation_completed']:
            if not self.run_evaluation():
                return False
        
        # 6. 生成报告
        self.generate_report()
        
        self.logger.info("=" * 60)
        self.logger.info("🎉 FlowerCount-YOLO项目流程完成！")
        self.logger.info("📊 项目状态:")
        for key, value in self.status.items():
            status_icon = "✅" if value else "❌"
            self.logger.info(f"   {status_icon} {key}: {value}")
        
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FlowerCount-YOLO项目管理器")
    parser.add_argument("--config", default="flowercount_yolo_config.yaml", 
                       help="配置文件路径")
    parser.add_argument("--action", choices=[
        "full", "annotation", "split", "train", "evaluate", "report"
    ], default="full", help="执行的操作")
    parser.add_argument("--stage", choices=[
        "warmup", "detection", "full", "all"
    ], default="all", help="训练阶段")
    
    args = parser.parse_args()
    
    # 创建项目管理器
    manager = FlowerCountYOLOManager(args.config)
    
    # 执行指定操作
    success = False
    if args.action == "full":
        success = manager.run_full_pipeline()
    elif args.action == "annotation":
        success = manager.run_annotation()
    elif args.action == "split":
        success = manager.run_dataset_split()
    elif args.action == "train":
        success = manager.run_training(args.stage)
    elif args.action == "evaluate":
        success = manager.run_evaluation()
    elif args.action == "report":
        success = manager.generate_report()
    
    if success:
        print("\n🎉 操作完成成功！")
        sys.exit(0)
    else:
        print("\n❌ 操作失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
