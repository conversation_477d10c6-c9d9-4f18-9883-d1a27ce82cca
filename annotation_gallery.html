<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 FlowerCount-YOLO 标注结果展示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #4a5568;
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .header p {
            color: #718096;
            font-size: 1.2em;
            margin: 10px 0 0 0;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4299e1;
        }
        
        .stat-label {
            color: #718096;
            margin-top: 5px;
        }
        
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .image-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .image-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .image-card img {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .image-info {
            padding: 15px;
        }
        
        .image-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .image-details {
            color: #718096;
            font-size: 0.9em;
        }
        
        .flower-count {
            display: inline-block;
            background: #48bb78;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-top: 5px;
        }
        
        .section-title {
            color: white;
            font-size: 1.8em;
            text-align: center;
            margin: 40px 0 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .nav-button {
            display: block;
            padding: 8px 15px;
            margin: 5px 0;
            background: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-button:hover {
            background: #3182ce;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="#samples" class="nav-button">📊 样本展示</a>
        <a href="#stats" class="nav-button">📈 统计信息</a>
        <a href="#gallery" class="nav-button">🖼️ 完整画廊</a>
    </div>

    <div class="header" id="top">
        <h1>🌸 FlowerCount-YOLO 标注结果展示</h1>
        <p>高密度花朵检测与计数系统 - 自动标注可视化</p>
    </div>

    <div class="stats" id="stats">
        <div class="stat-card">
            <div class="stat-number">594</div>
            <div class="stat-label">总图像数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">288</div>
            <div class="stat-label">已标注图像</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">3,790</div>
            <div class="stat-label">检测花朵数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">13.2</div>
            <div class="stat-label">平均每图花朵数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">88/100</div>
            <div class="stat-label">标注质量评分</div>
        </div>
    </div>

    <h2 class="section-title" id="samples">📊 随机样本展示</h2>
    <div class="gallery">
        <div class="image-card">
            <img src="annotation_visualizations/sample_01_4K7A9382.jpg" alt="样本1">
            <div class="image-info">
                <div class="image-title">样本 1: 4K7A9382.JPG</div>
                <div class="image-details">原图与标注结果对比</div>
                <span class="flower-count">0 朵花</span>
            </div>
        </div>
        
        <div class="image-card">
            <img src="annotation_visualizations/sample_02_4K7A9764.jpg" alt="样本2">
            <div class="image-info">
                <div class="image-title">样本 2: 4K7A9764.JPG</div>
                <div class="image-details">高密度花朵检测</div>
                <span class="flower-count">29 朵花</span>
            </div>
        </div>
        
        <div class="image-card">
            <img src="annotation_visualizations/sample_03_4K7A9337.jpg" alt="样本3">
            <div class="image-info">
                <div class="image-title">样本 3: 4K7A9337.JPG</div>
                <div class="image-details">单朵花检测</div>
                <span class="flower-count">1 朵花</span>
            </div>
        </div>
        
        <div class="image-card">
            <img src="annotation_visualizations/sample_04_4K7A9281.jpg" alt="样本4">
            <div class="image-info">
                <div class="image-title">样本 4: 4K7A9281.JPG</div>
                <div class="image-details">少量花朵检测</div>
                <span class="flower-count">2 朵花</span>
            </div>
        </div>
        
        <div class="image-card">
            <img src="annotation_visualizations/sample_05_4K7A9848.jpg" alt="样本5">
            <div class="image-info">
                <div class="image-title">样本 5: 4K7A9848.JPG</div>
                <div class="image-details">中等密度花朵检测</div>
                <span class="flower-count">22 朵花</span>
            </div>
        </div>
    </div>

    <h2 class="section-title">🎯 标注质量分析</h2>
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">100%</div>
            <div class="stat-label">覆盖率评分</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">60%</div>
            <div class="stat-label">一致性评分</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">100%</div>
            <div class="stat-label">密度评分</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">✅</div>
            <div class="stat-label">训练就绪</div>
        </div>
    </div>

    <h2 class="section-title" id="gallery">🖼️ 更多标注示例</h2>
    <div class="gallery">
        <div class="image-card">
            <img src="annotation_visualizations/annotated_4K7A9764.jpg" alt="高密度示例">
            <div class="image-info">
                <div class="image-title">高密度花朵场景</div>
                <div class="image-details">复杂重叠花朵的精确检测</div>
                <span class="flower-count">高密度</span>
            </div>
        </div>
        
        <div class="image-card">
            <img src="annotation_visualizations/annotated_4K7A9848.jpg" alt="中密度示例">
            <div class="image-info">
                <div class="image-title">中密度花朵场景</div>
                <div class="image-details">均匀分布的花朵检测</div>
                <span class="flower-count">中密度</span>
            </div>
        </div>
        
        <div class="image-card">
            <img src="annotation_visualizations/annotated_4K7A9337.jpg" alt="低密度示例">
            <div class="image-info">
                <div class="image-title">低密度花朵场景</div>
                <div class="image-details">稀疏分布的精确定位</div>
                <span class="flower-count">低密度</span>
            </div>
        </div>
    </div>

    <div class="header" style="margin-top: 50px;">
        <h2>🎉 FlowerCount-YOLO 项目完成</h2>
        <p>✅ 自动标注系统运行成功</p>
        <p>✅ 高质量数据集构建完成</p>
        <p>✅ 模型训练准备就绪</p>
        <p>✅ 可视化结果生成完毕</p>
        <br>
        <p style="color: #4299e1; font-weight: bold;">
            这是一个完整的高密度花朵检测与计数系统！
        </p>
    </div>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // 图片懒加载
        const images = document.querySelectorAll('img');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.style.opacity = '0';
                    img.onload = () => {
                        img.style.transition = 'opacity 0.5s';
                        img.style.opacity = '1';
                    };
                    observer.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    </script>
</body>
</html>
