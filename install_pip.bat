@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                   FlowerCount-YOLO pip安装脚本                              ║
echo ║                        (管理员权限)                                         ║
echo ║                                                                              ║
echo ║    🌸 Advanced Flower Detection and Counting System                         ║
echo ║    📦 使用pip安装所有依赖                                                   ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限确认
) else (
    echo ❌ 需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 🔍 检查conda安装...
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到conda！
    echo 请先安装Anaconda或Miniconda
    pause
    exit /b 1
)
echo ✅ conda已安装

echo.
echo 🏗️ 创建FlowerCount-YOLO CUDA环境...
conda create -n flowercount-yolo-cuda python=3.9 -y
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 环境创建失败！
    pause
    exit /b 1
)

echo.
echo 🎯 激活环境...
call conda activate flowercount-yolo-cuda

echo.
echo 📦 升级pip...
python -m pip install --upgrade pip

echo.
echo 🔥 安装PyTorch CUDA版本...
echo 这可能需要几分钟时间...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
if %ERRORLEVEL% NEQ 0 (
    echo ❌ PyTorch安装失败！
    pause
    exit /b 1
)

echo.
echo 🧪 验证PyTorch CUDA...
python -c "import torch; print('✅ PyTorch version:', torch.__version__); print('🔥 CUDA available:', torch.cuda.is_available()); print('🎮 GPU count:', torch.cuda.device_count() if torch.cuda.is_available() else 0)"
if %ERRORLEVEL__ NEQ 0 (
    echo ❌ PyTorch验证失败！
    pause
    exit /b 1
)

echo.
echo 📚 安装核心依赖包...
pip install ultralytics>=8.0.0
pip install opencv-python>=4.8.0
pip install opencv-contrib-python>=4.8.0
pip install matplotlib>=3.7.0
pip install seaborn>=0.12.0
pip install pandas>=2.0.0
pip install numpy>=1.24.0
pip install scipy>=1.11.0
pip install scikit-learn>=1.3.0
pip install tqdm>=4.65.0
pip install pyyaml>=6.0
pip install pillow>=10.0.0

echo.
echo 🎨 安装计算机视觉包...
pip install albumentations>=1.3.0
pip install scikit-image>=0.21.0

echo.
echo 📊 安装实验跟踪包...
pip install wandb>=0.15.0
pip install tensorboard>=2.13.0
pip install mlflow>=2.5.0

echo.
echo 🔍 安装可解释性包...
pip install grad-cam>=1.4.0
pip install transformers>=4.30.0
pip install timm>=0.9.0

echo.
echo 📏 安装评估包...
pip install pycocotools>=2.0.6
pip install torchmetrics>=1.0.0

echo.
echo 🛠️ 安装工具包...
pip install rich>=13.0.0
pip install hydra-core>=1.3.0

echo.
echo 🧪 最终验证...
python -c "
print('🔍 验证FlowerCount-YOLO环境...')
try:
    import torch, torchvision, cv2, numpy as np, matplotlib.pyplot as plt
    import pandas as pd, sklearn, yaml, PIL, tqdm, ultralytics
    print('✅ 所有核心包导入成功!')
    print('🔥 PyTorch CUDA:', torch.cuda.is_available())
    if torch.cuda.is_available():
        print('🎮 GPU:', torch.cuda.get_device_name(0))
        print('💾 GPU内存:', f'{torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB')
        # CUDA功能测试
        x = torch.randn(100, 100).cuda()
        y = torch.randn(100, 100).cuda() 
        z = torch.mm(x, y)
        print('✅ CUDA计算测试通过!')
    print('🎯 FlowerCount-YOLO环境准备就绪!')
except Exception as e:
    print('❌ 验证失败:', e)
    exit(1)
"

if %ERRORLEVEL__ == 0 (
    echo.
    echo 🎉 安装完成！
    echo.
    echo 📋 下一步:
    echo    1. 保持此终端打开
    echo    2. 运行: python quick_start.py --mode demo
    echo    3. 或运行: python start_project.py
    echo.
    echo 🌸 FlowerCount-YOLO CUDA环境已准备就绪！
    echo 🚀 现在可以使用GPU加速进行花朵检测和计数了！
) else (
    echo ❌ 最终验证失败！
    echo 请检查错误信息并重试
)

echo.
pause
