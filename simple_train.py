#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的YOLO训练脚本
"""

import os
import sys
from pathlib import Path
from ultralytics import YOLO

def main():
    print("🌸 开始简单YOLO训练")
    print("=" * 40)
    
    # 检查数据集
    dataset_config = Path("dataset/dataset.yaml")
    if not dataset_config.exists():
        print("❌ 数据集配置不存在")
        return False
    
    print(f"✅ 数据集配置: {dataset_config}")
    
    # 创建模型
    print("🎯 创建YOLO模型...")
    model = YOLO('yolov8n.pt')
    print("✅ 模型创建成功")
    
    # 开始训练
    print("🚀 开始训练...")
    try:
        results = model.train(
            data=str(dataset_config),
            epochs=10,  # 先用少量epoch测试
            imgsz=640,
            batch=2,    # 小批次
            device='auto',
            workers=2,
            patience=5,
            save=True,
            val=True,
            plots=True,
            verbose=True,
            project='runs/train',
            name='simple_test',
            exist_ok=True
        )
        
        print("🎉 训练完成！")
        print(f"模型保存在: {results.save_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 训练成功完成！")
    else:
        print("❌ 训练失败！")
