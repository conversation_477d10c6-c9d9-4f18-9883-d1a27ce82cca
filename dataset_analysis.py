#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 数据集分析工具
用于分析花朵图像数据集的详细统计信息
"""

import os
import json
import numpy as np
import cv2
from pathlib import Path
import matplotlib.pyplot as plt
# import seaborn as sns  # 可选依赖
from PIL import Image, ExifTags
import pandas as pd
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class DatasetAnalyzer:
    """数据集分析器"""
    
    def __init__(self, data_dir="data"):
        self.data_dir = Path(data_dir)
        self.results = {}
        self.image_files = []
        self.stats = defaultdict(list)
        
    def find_images(self):
        """查找所有图像文件"""
        print("🔍 扫描图像文件...")
        
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        for ext in image_extensions:
            self.image_files.extend(list(self.data_dir.glob(f"*{ext}")))
            self.image_files.extend(list(self.data_dir.glob(f"*{ext.upper()}")))
        
        print(f"   ✅ 找到 {len(self.image_files)} 张图像")
        return len(self.image_files)
    
    def analyze_basic_stats(self):
        """分析基本统计信息"""
        print("\n📊 分析基本统计信息...")
        
        total_size = 0
        resolutions = []
        aspect_ratios = []
        file_sizes = []
        
        for img_file in self.image_files:
            try:
                # 文件大小
                file_size = img_file.stat().st_size / (1024 * 1024)  # MB
                file_sizes.append(file_size)
                total_size += file_size
                
                # 图像尺寸
                with Image.open(img_file) as img:
                    width, height = img.size
                    resolutions.append((width, height))
                    aspect_ratios.append(width / height)
                    
                    # 存储统计信息
                    self.stats['widths'].append(width)
                    self.stats['heights'].append(height)
                    self.stats['file_sizes'].append(file_size)
                    self.stats['aspect_ratios'].append(width / height)
                    
            except Exception as e:
                print(f"   ⚠️  无法处理 {img_file.name}: {e}")
        
        # 计算统计结果
        self.results['basic_stats'] = {
            'total_images': len(self.image_files),
            'total_size_mb': round(total_size, 2),
            'avg_file_size_mb': round(np.mean(file_sizes), 2),
            'resolution_stats': {
                'min_width': int(np.min(self.stats['widths'])),
                'max_width': int(np.max(self.stats['widths'])),
                'avg_width': int(np.mean(self.stats['widths'])),
                'min_height': int(np.min(self.stats['heights'])),
                'max_height': int(np.max(self.stats['heights'])),
                'avg_height': int(np.mean(self.stats['heights'])),
            },
            'aspect_ratio_stats': {
                'min_ratio': round(np.min(aspect_ratios), 3),
                'max_ratio': round(np.max(aspect_ratios), 3),
                'avg_ratio': round(np.mean(aspect_ratios), 3),
            }
        }
        
        print(f"   📈 总图像数: {self.results['basic_stats']['total_images']}")
        print(f"   💾 总大小: {self.results['basic_stats']['total_size_mb']} MB")
        print(f"   📐 平均分辨率: {self.results['basic_stats']['resolution_stats']['avg_width']}x{self.results['basic_stats']['resolution_stats']['avg_height']}")
        
    def analyze_color_distribution(self):
        """分析颜色分布"""
        print("\n🎨 分析颜色分布...")
        
        color_stats = {
            'red_dominant': 0,
            'green_dominant': 0,
            'blue_dominant': 0,
            'avg_brightness': [],
            'avg_saturation': [],
            'avg_hue': []
        }
        
        sample_size = min(50, len(self.image_files))  # 采样分析
        sample_files = np.random.choice(self.image_files, sample_size, replace=False)
        
        for img_file in sample_files:
            try:
                img = cv2.imread(str(img_file))
                if img is None:
                    continue
                    
                # 转换到HSV
                hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
                
                # 计算平均值
                avg_hue = np.mean(hsv[:,:,0])
                avg_saturation = np.mean(hsv[:,:,1])
                avg_brightness = np.mean(hsv[:,:,2])
                
                color_stats['avg_hue'].append(avg_hue)
                color_stats['avg_saturation'].append(avg_saturation)
                color_stats['avg_brightness'].append(avg_brightness)
                
                # 分析主导颜色
                bgr_mean = np.mean(img, axis=(0,1))
                dominant_channel = np.argmax(bgr_mean)
                if dominant_channel == 0:
                    color_stats['blue_dominant'] += 1
                elif dominant_channel == 1:
                    color_stats['green_dominant'] += 1
                else:
                    color_stats['red_dominant'] += 1
                    
            except Exception as e:
                continue
        
        self.results['color_stats'] = {
            'avg_brightness': round(np.mean(color_stats['avg_brightness']), 2),
            'avg_saturation': round(np.mean(color_stats['avg_saturation']), 2),
            'avg_hue': round(np.mean(color_stats['avg_hue']), 2),
            'dominant_colors': {
                'red': color_stats['red_dominant'],
                'green': color_stats['green_dominant'],
                'blue': color_stats['blue_dominant']
            }
        }
        
        print(f"   🌈 平均亮度: {self.results['color_stats']['avg_brightness']}")
        print(f"   🎨 平均饱和度: {self.results['color_stats']['avg_saturation']}")
        
    def estimate_flower_density(self):
        """估计花朵密度分布"""
        print("\n🌸 估计花朵密度分布...")
        
        density_stats = {
            'low_density': 0,    # 1-5朵
            'medium_density': 0, # 6-15朵
            'high_density': 0,   # 16+朵
            'estimated_counts': []
        }
        
        sample_size = min(30, len(self.image_files))
        sample_files = np.random.choice(self.image_files, sample_size, replace=False)
        
        for img_file in sample_files:
            try:
                img = cv2.imread(str(img_file))
                if img is None:
                    continue
                
                # 使用颜色分割估计花朵数量
                estimated_count = self._estimate_flower_count(img)
                density_stats['estimated_counts'].append(estimated_count)
                
                if estimated_count <= 5:
                    density_stats['low_density'] += 1
                elif estimated_count <= 15:
                    density_stats['medium_density'] += 1
                else:
                    density_stats['high_density'] += 1
                    
            except Exception as e:
                continue
        
        self.results['density_stats'] = {
            'avg_flower_count': round(np.mean(density_stats['estimated_counts']), 1),
            'max_flower_count': int(np.max(density_stats['estimated_counts'])),
            'min_flower_count': int(np.min(density_stats['estimated_counts'])),
            'density_distribution': {
                'low_density_images': density_stats['low_density'],
                'medium_density_images': density_stats['medium_density'],
                'high_density_images': density_stats['high_density']
            }
        }
        
        print(f"   🌸 平均花朵数: {self.results['density_stats']['avg_flower_count']}")
        print(f"   📊 密度分布: 低密度({density_stats['low_density']}) 中密度({density_stats['medium_density']}) 高密度({density_stats['high_density']})")
        
    def _estimate_flower_count(self, img):
        """使用简单的颜色分割估计花朵数量"""
        try:
            height, width = img.shape[:2]
            hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
            
            # 定义花朵颜色范围
            color_ranges = [
                ([0, 50, 50], [10, 255, 255]),    # 红色
                ([170, 50, 50], [180, 255, 255]), # 红色
                ([140, 50, 50], [170, 255, 255]), # 粉色
                ([15, 50, 50], [35, 255, 255]),   # 黄色
                ([120, 50, 50], [140, 255, 255]), # 紫色
                ([0, 0, 200], [180, 30, 255]),    # 白色
            ]
            
            combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            for lower, upper in color_ranges:
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                combined_mask = cv2.bitwise_or(combined_mask, mask)
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤轮廓
            min_area = 300
            max_area = width * height * 0.2
            flower_count = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area < area < max_area:
                    flower_count += 1
            
            return flower_count
            
        except:
            return 5  # 默认估计值

    def generate_visualizations(self):
        """生成可视化图表"""
        print("\n📊 生成可视化图表...")

        # 创建输出目录
        viz_dir = Path("analysis_results")
        viz_dir.mkdir(exist_ok=True)

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 1. 分辨率分布图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 宽度分布
        axes[0,0].hist(self.stats['widths'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0,0].set_title('图像宽度分布', fontsize=14, fontweight='bold')
        axes[0,0].set_xlabel('宽度 (像素)')
        axes[0,0].set_ylabel('频次')
        axes[0,0].grid(True, alpha=0.3)

        # 高度分布
        axes[0,1].hist(self.stats['heights'], bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[0,1].set_title('图像高度分布', fontsize=14, fontweight='bold')
        axes[0,1].set_xlabel('高度 (像素)')
        axes[0,1].set_ylabel('频次')
        axes[0,1].grid(True, alpha=0.3)

        # 宽高比分布
        axes[1,0].hist(self.stats['aspect_ratios'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[1,0].set_title('宽高比分布', fontsize=14, fontweight='bold')
        axes[1,0].set_xlabel('宽高比')
        axes[1,0].set_ylabel('频次')
        axes[1,0].grid(True, alpha=0.3)

        # 文件大小分布
        axes[1,1].hist(self.stats['file_sizes'], bins=20, alpha=0.7, color='gold', edgecolor='black')
        axes[1,1].set_title('文件大小分布', fontsize=14, fontweight='bold')
        axes[1,1].set_xlabel('文件大小 (MB)')
        axes[1,1].set_ylabel('频次')
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(viz_dir / 'dataset_statistics.png', dpi=300, bbox_inches='tight')
        plt.close()

        print(f"   ✅ 保存统计图表: {viz_dir / 'dataset_statistics.png'}")

    def save_results(self):
        """保存分析结果"""
        print("\n💾 保存分析结果...")

        # 创建输出目录
        output_dir = Path("analysis_results")
        output_dir.mkdir(exist_ok=True)

        # 保存JSON结果
        with open(output_dir / 'dataset_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

        # 生成详细报告
        report = self._generate_report()
        with open(output_dir / 'dataset_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"   ✅ 保存分析结果: {output_dir / 'dataset_analysis.json'}")
        print(f"   ✅ 保存详细报告: {output_dir / 'dataset_report.txt'}")

    def _generate_report(self):
        """生成详细报告"""
        report = f"""
# FlowerCount-YOLO 数据集分析报告

## 📊 基本统计信息

- **总图像数**: {self.results['basic_stats']['total_images']} 张
- **总数据大小**: {self.results['basic_stats']['total_size_mb']} MB
- **平均文件大小**: {self.results['basic_stats']['avg_file_size_mb']} MB

## 📐 分辨率统计

- **宽度范围**: {self.results['basic_stats']['resolution_stats']['min_width']} - {self.results['basic_stats']['resolution_stats']['max_width']} 像素
- **平均宽度**: {self.results['basic_stats']['resolution_stats']['avg_width']} 像素
- **高度范围**: {self.results['basic_stats']['resolution_stats']['min_height']} - {self.results['basic_stats']['resolution_stats']['max_height']} 像素
- **平均高度**: {self.results['basic_stats']['resolution_stats']['avg_height']} 像素

## 🎨 颜色分析

- **平均亮度**: {self.results['color_stats']['avg_brightness']}
- **平均饱和度**: {self.results['color_stats']['avg_saturation']}
- **平均色调**: {self.results['color_stats']['avg_hue']}

## 🌸 花朵密度估计

- **平均花朵数**: {self.results['density_stats']['avg_flower_count']} 朵/图像
- **花朵数范围**: {self.results['density_stats']['min_flower_count']} - {self.results['density_stats']['max_flower_count']} 朵
- **密度分布**:
  - 低密度图像 (1-5朵): {self.results['density_stats']['density_distribution']['low_density_images']} 张
  - 中密度图像 (6-15朵): {self.results['density_stats']['density_distribution']['medium_density_images']} 张
  - 高密度图像 (16+朵): {self.results['density_stats']['density_distribution']['high_density_images']} 张

## 📈 数据集质量评估

### ✅ 优势
- 图像数量充足 ({self.results['basic_stats']['total_images']} 张)
- 分辨率较高 (平均 {self.results['basic_stats']['resolution_stats']['avg_width']}x{self.results['basic_stats']['resolution_stats']['avg_height']})
- 花朵密度分布合理

### 🔧 建议
- 建议使用自动标注系统生成高质量标注
- 可考虑数据增强提高模型泛化能力
- 建议按密度分层进行训练集划分

---
生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        return report

    def run_analysis(self):
        """运行完整分析"""
        print("🚀 开始数据集分析...")
        print("=" * 50)

        # 查找图像
        if self.find_images() == 0:
            print("❌ 未找到图像文件，请检查data目录")
            return False

        # 执行各项分析
        self.analyze_basic_stats()
        self.analyze_color_distribution()
        self.estimate_flower_density()
        self.generate_visualizations()
        self.save_results()

        print("\n" + "=" * 50)
        print("✅ 数据集分析完成！")
        print(f"📁 结果保存在: analysis_results/")

        return True

if __name__ == "__main__":
    analyzer = DatasetAnalyzer()
    analyzer.run_analysis()
