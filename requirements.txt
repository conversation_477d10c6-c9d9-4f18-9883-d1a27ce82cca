# Advanced Flower Detection and Counting System Requirements

# Core Deep Learning Frameworks
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0
ultralytics>=8.0.0

# Computer Vision and Image Processing
opencv-python>=4.8.0
opencv-contrib-python>=4.8.0
Pillow>=10.0.0
albumentations>=1.3.0
scikit-image>=0.21.0

# Scientific Computing
numpy>=1.24.0
scipy>=1.11.0
scikit-learn>=1.3.0
pandas>=2.0.0

# Visualization and Plotting
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Experiment Tracking and Logging
wandb>=0.15.0
tensorboard>=2.13.0
mlflow>=2.5.0

# Auto-annotation and Foundation Models
segment-anything>=1.0
transformers>=4.30.0
clip-by-openai>=1.0
timm>=0.9.0

# Evaluation and Metrics
pycocotools>=2.0.6
torchmetrics>=1.0.0

# Utilities
tqdm>=4.65.0
pyyaml>=6.0
requests>=2.31.0
rich>=13.0.0
hydra-core>=1.3.0

# Model Optimization
onnx>=1.14.0
onnxruntime>=1.15.0

# Explainability and Visualization
grad-cam>=1.4.0
captum>=0.6.0
lime>=0.2.0

# Statistical Analysis
statsmodels>=0.14.0
pingouin>=0.5.0
