
FlowerCount-YOLO 架构概述
========================

1. 输入层 (Input Layer)
   - 图像尺寸: 640×640×3
   - 数据预处理: 归一化、数据增强

2. 骨干网络 (Backbone)
   - 基于YOLOv10的CSPDarknet
   - 多尺度特征提取
   - 残差连接和跨阶段部分连接

3. 注意力机制 (Attention Mechanism)
   - CBAM (Convolutional Block Attention Module)
   - 通道注意力 + 空间注意力
   - 自适应特征增强

4. 特征融合 (Feature Fusion)
   - FPN (Feature Pyramid Network)
   - PAN (Path Aggregation Network)
   - 多尺度特征整合

5. 检测头 (Detection Head)
   - 边界框回归
   - 分类预测
   - 置信度估计

6. 密度估计分支 (Density Estimation)
   - 高斯核密度建模
   - 精确计数预测
   - 密集场景处理

7. 损失函数 (Loss Functions)
   - 检测损失: IoU + 分类损失
   - 计数损失: MSE
   - 密度损失: MSE + SSIM

8. 训练策略 (Training Strategy)
   - 渐进式三阶段训练
   - 数据增强
   - 学习率调度

关键创新点:
- 🎯 CBAM注意力机制增强特征表示
- 🔄 多尺度特征融合优化检测性能
- 📊 密度估计分支提高计数精度
- 📈 渐进式训练策略优化收敛
    