#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 高密度花朵自动标注系统
专门针对高密度花朵场景优化的标注生成器
"""

import os
import json
import numpy as np
import cv2
from pathlib import Path
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple
import warnings
warnings.filterwarnings('ignore')

class HighDensityFlowerAnnotator:
    """高密度花朵自动标注器"""
    
    def __init__(self, 
                 confidence_threshold: float = 0.4,
                 nms_threshold: float = 0.3,
                 min_area: int = 200,
                 max_area_ratio: float = 0.05):
        """
        初始化高密度花朵标注器
        
        Args:
            confidence_threshold: 置信度阈值
            nms_threshold: NMS阈值 (较低以保留更多检测)
            min_area: 最小区域面积
            max_area_ratio: 最大区域面积比例
        """
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        self.min_area = min_area
        self.max_area_ratio = max_area_ratio
        
        # 针对高密度场景优化的花朵颜色范围
        self.flower_color_ranges = [
            # 红色花朵 (主要颜色，扩大范围)
            ([0, 30, 30], [15, 255, 255]),
            ([165, 30, 30], [180, 255, 255]),
            # 粉色花朵
            ([135, 20, 40], [175, 255, 255]),
            # 黄色花朵
            ([12, 30, 40], [40, 255, 255]),
            # 橙色花朵
            ([5, 30, 40], [30, 255, 255]),
            # 紫色花朵
            ([115, 20, 40], [145, 255, 255]),
            # 白色/浅色花朵 (重要：捕获浅色花朵)
            ([0, 0, 120], [180, 50, 255]),
            # 蓝色花朵
            ([95, 30, 40], [125, 255, 255]),
        ]
        
        print("🌸 HighDensityFlowerAnnotator 初始化完成")
        print(f"   📊 参数: conf={confidence_threshold}, nms={nms_threshold}")
        
    def detect_flowers_multiscale(self, image_path: str) -> List[Dict]:
        """
        多尺度花朵检测
        
        Args:
            image_path: 图像路径
            
        Returns:
            检测结果列表
        """
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                return []
            
            height, width = img.shape[:2]
            print(f"   🖼️  处理图像: {os.path.basename(image_path)} ({width}x{height})")
            
            # 多尺度检测
            all_detections = []
            scales = [1.0, 0.8, 0.6, 0.4]  # 多个尺度
            
            for scale in scales:
                if scale < 1.0:
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    img_scaled = cv2.resize(img, (new_width, new_height))
                else:
                    img_scaled = img.copy()
                    new_width, new_height = width, height
                
                # 在当前尺度检测
                detections = self._detect_at_scale(img_scaled, scale, width, height)
                all_detections.extend(detections)
            
            # 应用NMS合并多尺度结果
            final_detections = self.apply_nms(all_detections)
            
            print(f"   ✅ 检测到 {len(final_detections)} 朵花")
            return final_detections
            
        except Exception as e:
            print(f"   ❌ 检测失败 {os.path.basename(image_path)}: {e}")
            return []
    
    def _detect_at_scale(self, img: np.ndarray, scale: float, 
                        orig_width: int, orig_height: int) -> List[Dict]:
        """在特定尺度下检测花朵"""
        height, width = img.shape[:2]
        
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 合并所有颜色掩码
        combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
        for lower, upper in self.flower_color_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        # 自适应形态学操作
        kernel_size = max(3, int(7 * scale))
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        detections = []
        min_area_scaled = self.min_area * (scale ** 2)
        max_area_scaled = width * height * self.max_area_ratio
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area_scaled < area < max_area_scaled:
                # 计算轮廓质量
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    compactness = 4 * np.pi * area / (perimeter * perimeter)
                    if compactness > 0.15:  # 较宽松的形状要求
                        # 获取边界框
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        # 转换回原始图像坐标
                        x = int(x / scale)
                        y = int(y / scale)
                        w = int(w / scale)
                        h = int(h / scale)
                        
                        # 转换为YOLO格式
                        center_x = (x + w/2) / orig_width
                        center_y = (y + h/2) / orig_height
                        rel_width = w / orig_width
                        rel_height = h / orig_height
                        
                        # 确保坐标在有效范围内
                        if (0 < center_x < 1 and 0 < center_y < 1 and 
                            rel_width > 0 and rel_height > 0):
                            
                            # 计算置信度
                            confidence = min(0.9, compactness * 1.5 + scale * 0.3)
                            
                            detection = {
                                'class_id': 0,
                                'center_x': center_x,
                                'center_y': center_y,
                                'width': rel_width,
                                'height': rel_height,
                                'confidence': confidence,
                                'area': area / (scale ** 2),
                                'bbox': [x, y, w, h],
                                'scale': scale
                            }
                            detections.append(detection)
        
        return detections
    
    def apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """应用非极大值抑制"""
        if not detections:
            return []
        
        # 提取边界框和置信度
        boxes = []
        scores = []
        
        for det in detections:
            x, y, w, h = det['bbox']
            boxes.append([x, y, x+w, y+h])
            scores.append(det['confidence'])
        
        boxes = np.array(boxes, dtype=np.float32)
        scores = np.array(scores, dtype=np.float32)
        
        # 应用NMS
        indices = cv2.dnn.NMSBoxes(
            boxes.tolist(), 
            scores.tolist(), 
            self.confidence_threshold, 
            self.nms_threshold
        )
        
        if len(indices) > 0:
            indices = indices.flatten()
            return [detections[i] for i in indices]
        else:
            return []
    
    def save_yolo_annotations(self, detections: List[Dict], 
                             image_path: str, output_dir: str = "data/labels"):
        """保存YOLO格式标注"""
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        image_name = Path(image_path).stem
        label_file = output_dir / f"{image_name}.txt"
        
        with open(label_file, 'w') as f:
            for det in detections:
                f.write(f"{det['class_id']} {det['center_x']:.6f} {det['center_y']:.6f} "
                       f"{det['width']:.6f} {det['height']:.6f}\n")
        
        return str(label_file)
    
    def visualize_detections(self, image_path: str, detections: List[Dict], 
                           output_path: str = None):
        """可视化检测结果"""
        img = cv2.imread(image_path)
        if img is None:
            return
        
        height, width = img.shape[:2]
        
        # 绘制检测框
        for det in detections:
            x, y, w, h = det['bbox']
            confidence = det['confidence']
            
            # 绘制边界框
            color = (0, 255, 0) if confidence > 0.7 else (0, 255, 255)
            cv2.rectangle(img, (x, y), (x+w, y+h), color, 2)
            
            # 绘制置信度
            cv2.putText(img, f"{confidence:.2f}", (x, y-5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        # 保存结果
        if output_path is None:
            output_path = f"visualization_{Path(image_path).stem}.jpg"
        
        cv2.imwrite(output_path, img)
        return output_path
    
    def process_dataset(self, data_dir: str = "data", 
                       output_dir: str = "data/labels",
                       visualize: bool = True):
        """处理整个数据集"""
        print("🚀 开始处理数据集...")
        print("=" * 50)
        
        data_path = Path(data_dir)
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        
        # 查找所有图像
        image_files = []
        for ext in image_extensions:
            image_files.extend(list(data_path.glob(f"*{ext}")))
            image_files.extend(list(data_path.glob(f"*{ext.upper()}")))
        
        if not image_files:
            print("❌ 未找到图像文件")
            return
        
        print(f"📁 找到 {len(image_files)} 张图像")
        
        # 创建输出目录
        Path(output_dir).mkdir(exist_ok=True)
        if visualize:
            Path("annotation_visualizations").mkdir(exist_ok=True)
        
        total_detections = 0
        processed_images = 0
        
        # 处理每张图像
        for i, image_file in enumerate(image_files[:10]):  # 先处理前10张作为演示
            print(f"\n📸 处理 {i+1}/{min(10, len(image_files))}: {image_file.name}")
            
            # 检测花朵
            detections = self.detect_flowers_multiscale(str(image_file))
            
            if detections:
                # 保存标注
                label_file = self.save_yolo_annotations(detections, str(image_file), output_dir)
                print(f"   💾 保存标注: {Path(label_file).name}")
                
                # 可视化
                if visualize:
                    viz_path = f"annotation_visualizations/annotated_{image_file.stem}.jpg"
                    self.visualize_detections(str(image_file), detections, viz_path)
                    print(f"   🎨 保存可视化: {viz_path}")
                
                total_detections += len(detections)
                processed_images += 1
            else:
                # 创建空标注文件
                label_file = Path(output_dir) / f"{image_file.stem}.txt"
                with open(label_file, 'w') as f:
                    pass
                print(f"   ⚠️  未检测到花朵，创建空标注")
        
        print("\n" + "=" * 50)
        print("✅ 数据集处理完成！")
        print(f"📊 统计信息:")
        print(f"   - 处理图像: {processed_images}/{min(10, len(image_files))}")
        print(f"   - 总检测数: {total_detections}")
        print(f"   - 平均每图: {total_detections/max(1, processed_images):.1f} 朵花")
        print(f"📁 输出目录: {output_dir}")
        
        return {
            'processed_images': processed_images,
            'total_detections': total_detections,
            'avg_detections_per_image': total_detections/max(1, processed_images)
        }

if __name__ == "__main__":
    # 创建标注器
    annotator = HighDensityFlowerAnnotator(
        confidence_threshold=0.4,
        nms_threshold=0.3,
        min_area=200,
        max_area_ratio=0.05
    )
    
    # 处理数据集
    results = annotator.process_dataset(
        data_dir="data",
        output_dir="data/labels",
        visualize=True
    )
