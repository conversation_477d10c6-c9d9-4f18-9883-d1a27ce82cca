#!/usr/bin/env python3
"""
FlowerCount-YOLO pip安装验证脚本

验证所有依赖包是否正确安装，特别是CUDA支持
"""

import sys
import importlib
import subprocess
import platform


def print_banner():
    """打印验证横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    FlowerCount-YOLO pip安装验证                             ║
║                                                                              ║
║    🔍 检查所有依赖包和CUDA支持                                               ║
║    🌸 确保环境准备就绪                                                       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_package(package_name, display_name=None, required=True):
    """检查包是否安装"""
    if display_name is None:
        display_name = package_name
    
    try:
        pkg = importlib.import_module(package_name)
        version = getattr(pkg, '__version__', 'Unknown')
        status = "✅" if required else "🎯"
        print(f"   {status} {display_name}: {version}")
        return True
    except ImportError:
        status = "❌" if required else "⚠️ "
        print(f"   {status} {display_name}: {'未安装' if required else '可选包未安装'}")
        return False


def check_pytorch_cuda():
    """检查PyTorch CUDA支持"""
    print("\n🔥 PyTorch CUDA检查:")
    print("=" * 25)
    
    try:
        import torch
        
        print(f"   📦 PyTorch版本: {torch.__version__}")
        print(f"   🔥 CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"   📊 CUDA版本: {torch.version.cuda}")
            print(f"   🎯 GPU数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   🎮 GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
            # CUDA功能测试
            try:
                print("   🧪 执行CUDA计算测试...")
                x = torch.randn(1000, 1000).cuda()
                y = torch.randn(1000, 1000).cuda()
                z = torch.mm(x, y)
                torch.cuda.synchronize()
                print("   ✅ CUDA计算测试通过!")
                return True
            except Exception as e:
                print(f"   ❌ CUDA计算测试失败: {e}")
                return False
        else:
            print("   ⚠️  CUDA不可用 - 将使用CPU模式")
            return False
            
    except ImportError:
        print("   ❌ PyTorch未安装!")
        return False


def check_gpu_memory():
    """检查GPU内存"""
    try:
        import torch
        if torch.cuda.is_available():
            print("\n💾 GPU内存状态:")
            print("=" * 20)
            for i in range(torch.cuda.device_count()):
                total_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                cached = torch.cuda.memory_reserved(i) / 1024**3
                free = total_memory - cached
                
                print(f"   GPU {i}:")
                print(f"     总内存: {total_memory:.1f}GB")
                print(f"     已分配: {allocated:.1f}GB")
                print(f"     已缓存: {cached:.1f}GB")
                print(f"     可用: {free:.1f}GB")
                
                if free < 2.0:
                    print(f"     ⚠️  可用内存较少，建议释放GPU内存")
                else:
                    print(f"     ✅ 内存充足")
    except:
        pass


def test_model_loading():
    """测试模型加载"""
    print("\n🤖 模型加载测试:")
    print("=" * 20)
    
    try:
        from ultralytics import YOLO
        print("   ✅ Ultralytics YOLO导入成功")
        
        # 测试加载预训练模型
        try:
            model = YOLO('yolov8n.pt')  # 加载nano模型
            print("   ✅ YOLOv8模型加载成功")
            
            # 测试GPU推理
            import torch
            if torch.cuda.is_available():
                model.to('cuda')
                print("   ✅ 模型成功移至GPU")
            
            return True
        except Exception as e:
            print(f"   ⚠️  模型加载测试失败: {e}")
            return False
            
    except ImportError:
        print("   ❌ Ultralytics未安装")
        return False


def main():
    """主验证函数"""
    print_banner()
    
    # 系统信息
    print("🖥️  系统信息:")
    print("=" * 15)
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   Python版本: {sys.version}")
    print(f"   架构: {platform.machine()}")
    
    # 核心包检查
    print("\n📦 核心依赖包:")
    print("=" * 18)
    
    core_packages = [
        ('torch', 'PyTorch'),
        ('torchvision', 'TorchVision'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('matplotlib', 'Matplotlib'),
        ('pandas', 'Pandas'),
        ('sklearn', 'Scikit-learn'),
        ('yaml', 'PyYAML'),
        ('PIL', 'Pillow'),
        ('ultralytics', 'Ultralytics'),
        ('tqdm', 'TQDM')
    ]
    
    core_success = 0
    for pkg, name in core_packages:
        if check_package(pkg, name, required=True):
            core_success += 1
    
    # 可选包检查
    print("\n🎯 可选功能包:")
    print("=" * 18)
    
    optional_packages = [
        ('wandb', 'Weights & Biases'),
        ('tensorboard', 'TensorBoard'),
        ('transformers', 'Transformers'),
        ('grad_cam', 'GradCAM'),
        ('albumentations', 'Albumentations'),
        ('timm', 'TIMM'),
        ('mlflow', 'MLflow')
    ]
    
    optional_success = 0
    for pkg, name in optional_packages:
        if check_package(pkg, name, required=False):
            optional_success += 1
    
    # PyTorch CUDA检查
    cuda_ok = check_pytorch_cuda()
    
    # GPU内存检查
    check_gpu_memory()
    
    # 模型加载测试
    model_ok = test_model_loading()
    
    # 总结
    print("\n📋 验证总结:")
    print("=" * 15)
    print(f"   核心包: {core_success}/{len(core_packages)} ({'✅' if core_success >= len(core_packages) * 0.9 else '❌'})")
    print(f"   可选包: {optional_success}/{len(optional_packages)} ({'🎯' if optional_success >= len(optional_packages) * 0.5 else '⚠️'})")
    print(f"   CUDA支持: {'✅' if cuda_ok else '❌'}")
    print(f"   模型加载: {'✅' if model_ok else '❌'}")
    
    # 最终状态
    if core_success >= len(core_packages) * 0.9:
        print(f"\n🎉 环境验证成功!")
        print(f"🚀 FlowerCount-YOLO准备就绪!")
        
        if cuda_ok:
            print(f"🔥 GPU加速可用 - 推荐使用GPU训练")
        else:
            print(f"💻 将使用CPU模式 - 建议检查CUDA安装")
        
        print(f"\n📋 下一步:")
        print(f"   1. 运行演示: python quick_start.py --mode demo")
        print(f"   2. 开始训练: python quick_start.py --mode train")
        print(f"   3. 完整实验: python run_complete_experiment.py")
        
    else:
        print(f"\n❌ 环境验证失败!")
        print(f"🔧 请安装缺失的核心包:")
        
        missing_core = []
        for pkg, name in core_packages:
            try:
                importlib.import_module(pkg)
            except ImportError:
                missing_core.append(name)
        
        if missing_core:
            print(f"   pip install {' '.join(missing_core).lower()}")
    
    print(f"\n🌸 感谢使用FlowerCount-YOLO!")


if __name__ == "__main__":
    main()
