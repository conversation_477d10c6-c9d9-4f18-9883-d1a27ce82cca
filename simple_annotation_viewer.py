#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的标注查看器
显示原图和标注后的图像
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import random

def load_annotations(label_file):
    """加载YOLO格式的标注"""
    annotations = []
    if label_file.exists():
        with open(label_file, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    cls, x, y, w, h = map(float, parts[:5])
                    annotations.append((x, y, w, h))
    return annotations

def yolo_to_bbox(x_center, y_center, width, height, img_width, img_height):
    """将YOLO格式转换为边界框坐标"""
    x_center *= img_width
    y_center *= img_height
    width *= img_width
    height *= img_height
    
    x1 = int(x_center - width / 2)
    y1 = int(y_center - height / 2)
    x2 = int(x_center + width / 2)
    y2 = int(y_center + height / 2)
    
    return max(0, x1), max(0, y1), min(img_width, x2), min(img_height, y2)

def visualize_single_image(image_path, label_path, output_path):
    """可视化单张图像的标注"""
    print(f"处理: {image_path.name}")
    
    # 读取图像
    image = cv2.imread(str(image_path))
    if image is None:
        print(f"无法读取图像: {image_path}")
        return False
    
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    img_height, img_width = image.shape[:2]
    
    # 加载标注
    annotations = load_annotations(label_path)
    
    # 创建标注图像
    annotated_image = image.copy()
    
    # 绘制标注框
    for i, (x, y, w, h) in enumerate(annotations):
        x1, y1, x2, y2 = yolo_to_bbox(x, y, w, h, img_width, img_height)
        
        # 绘制边界框
        cv2.rectangle(annotated_image, (x1, y1), (x2, y2), (0, 255, 0), 3)
        
        # 添加编号
        cv2.putText(annotated_image, f'{i+1}', (x1, y1-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
    
    # 创建对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 原图
    ax1.imshow(image)
    ax1.set_title(f'原图: {image_path.name}', fontsize=12, fontweight='bold')
    ax1.axis('off')
    
    # 标注图
    ax2.imshow(annotated_image)
    ax2.set_title(f'标注结果: {len(annotations)} 朵花', fontsize=12, fontweight='bold')
    ax2.axis('off')
    
    # 添加信息
    info_text = f"尺寸: {img_width}×{img_height}\n花朵数: {len(annotations)}"
    plt.figtext(0.02, 0.02, info_text, fontsize=10,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    return True

def main():
    """主函数"""
    print("🌸 简单标注查看器")
    print("=" * 40)
    
    # 设置路径
    data_dir = Path("data")
    images_dir = data_dir
    labels_dir = data_dir / "labels"
    output_dir = Path("annotation_visualizations")
    output_dir.mkdir(exist_ok=True)
    
    print(f"图像目录: {images_dir}")
    print(f"标签目录: {labels_dir}")
    print(f"输出目录: {output_dir}")
    
    # 获取图像文件
    image_files = list(images_dir.glob("*.JPG")) + list(images_dir.glob("*.jpg"))
    print(f"找到 {len(image_files)} 张图像")
    
    if len(image_files) == 0:
        print("❌ 没有找到图像文件")
        return
    
    # 随机选择5张图像进行可视化
    num_samples = min(5, len(image_files))
    sample_files = random.sample(image_files, num_samples)
    
    print(f"\n🎨 可视化 {num_samples} 张随机图像...")
    
    success_count = 0
    total_flowers = 0
    
    for i, img_file in enumerate(sample_files):
        label_file = labels_dir / f"{img_file.stem}.txt"
        output_file = output_dir / f"sample_{i+1:02d}_{img_file.stem}.jpg"
        
        if visualize_single_image(img_file, label_file, output_file):
            success_count += 1
            annotations = load_annotations(label_file)
            total_flowers += len(annotations)
            print(f"   ✅ {img_file.name} -> {len(annotations)} 朵花")
        else:
            print(f"   ❌ {img_file.name} 处理失败")
    
    print(f"\n📊 处理结果:")
    print(f"   成功处理: {success_count}/{num_samples}")
    print(f"   总花朵数: {total_flowers}")
    print(f"   平均每图: {total_flowers/success_count:.1f} 朵" if success_count > 0 else "   平均每图: 0 朵")
    
    print(f"\n📁 可视化结果保存在: {output_dir}/")
    print("✅ 标注可视化完成！")

if __name__ == "__main__":
    main()
