#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 完整训练执行脚本
直接运行完整的训练流程，绕过任何重定向问题
"""

import os
import sys
import time
import json
from pathlib import Path
import torch
from ultralytics import YOLO
import warnings
warnings.filterwarnings('ignore')

def main():
    """主训练函数"""
    print("🌸 FlowerCount-YOLO 完整训练开始")
    print("=" * 60)
    
    # 检查环境
    print("🔍 检查训练环境...")
    print(f"   Python版本: {sys.version}")
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
    
    # 检查数据集
    print("\n📊 检查数据集...")
    dataset_config = Path("dataset/dataset.yaml")
    if not dataset_config.exists():
        print("❌ 数据集配置文件不存在")
        return False
    
    train_dir = Path("dataset/train/images")
    val_dir = Path("dataset/val/images")
    
    if not train_dir.exists() or not val_dir.exists():
        print("❌ 训练或验证目录不存在")
        return False
    
    train_images = list(train_dir.glob("*.jpg"))
    val_images = list(val_dir.glob("*.jpg"))
    
    print(f"   训练图像: {len(train_images)} 张")
    print(f"   验证图像: {len(val_images)} 张")
    
    if len(train_images) == 0:
        print("❌ 没有找到训练图像")
        return False
    
    # 创建YOLO模型
    print("\n🎯 创建YOLO模型...")
    try:
        model = YOLO('yolov8n.pt')
        print("   ✅ 模型创建成功")
    except Exception as e:
        print(f"   ❌ 模型创建失败: {e}")
        return False
    
    # 开始训练
    print("\n🚀 开始训练...")
    print("   训练参数:")
    print(f"   - 数据集: {dataset_config}")
    print(f"   - 轮数: 50")
    print(f"   - 图像尺寸: 640")
    print(f"   - 批次大小: 4")
    print(f"   - 设备: {'GPU' if torch.cuda.is_available() else 'CPU'}")
    
    start_time = time.time()
    
    try:
        # 训练模型
        results = model.train(
            data=str(dataset_config),
            epochs=50,
            imgsz=640,
            batch=4,
            device='auto',
            workers=4,
            patience=15,
            save=True,
            val=True,
            plots=True,
            verbose=True,
            project='runs/train',
            name='flowercount_complete',
            exist_ok=True,
            # 优化参数
            lr0=0.01,
            lrf=0.01,
            momentum=0.937,
            weight_decay=0.0005,
            warmup_epochs=3,
            warmup_momentum=0.8,
            warmup_bias_lr=0.1,
            box=7.5,
            cls=0.5,
            dfl=1.5,
            pose=12.0,
            kobj=1.0,
            label_smoothing=0.0,
            nbs=64,
            overlap_mask=True,
            mask_ratio=4,
            dropout=0.0,
            val_period=1,
            save_period=-1,
            # 数据增强
            hsv_h=0.015,
            hsv_s=0.7,
            hsv_v=0.4,
            degrees=0.0,
            translate=0.1,
            scale=0.5,
            shear=0.0,
            perspective=0.0,
            flipud=0.0,
            fliplr=0.5,
            mosaic=1.0,
            mixup=0.0,
            copy_paste=0.0
        )
        
        training_time = time.time() - start_time
        
        print(f"\n🎉 训练完成！")
        print(f"   训练时间: {training_time/60:.1f} 分钟")
        print(f"   模型保存目录: {results.save_dir}")
        
        # 保存训练信息
        train_info = {
            'model_path': str(results.save_dir / 'weights' / 'best.pt'),
            'results_dir': str(results.save_dir),
            'training_time_minutes': training_time / 60,
            'train_images': len(train_images),
            'val_images': len(val_images),
            'epochs': 50,
            'batch_size': 4,
            'image_size': 640,
            'device': 'GPU' if torch.cuda.is_available() else 'CPU',
            'final_metrics': {
                'mAP50': float(results.results_dict.get('metrics/mAP50(B)', 0)),
                'mAP50-95': float(results.results_dict.get('metrics/mAP50-95(B)', 0)),
                'precision': float(results.results_dict.get('metrics/precision(B)', 0)),
                'recall': float(results.results_dict.get('metrics/recall(B)', 0))
            } if hasattr(results, 'results_dict') else {}
        }
        
        with open('training_complete_info.json', 'w', encoding='utf-8') as f:
            json.dump(train_info, f, indent=2, ensure_ascii=False)
        
        print("   ✅ 训练信息已保存到 training_complete_info.json")
        
        # 运行验证
        print("\n📈 运行最终验证...")
        try:
            best_model = YOLO(str(results.save_dir / 'weights' / 'best.pt'))
            val_results = best_model.val(data=str(dataset_config))
            
            print(f"   最终验证结果:")
            if hasattr(val_results, 'results_dict'):
                metrics = val_results.results_dict
                print(f"   - mAP@0.5: {metrics.get('metrics/mAP50(B)', 0):.3f}")
                print(f"   - mAP@0.5:0.95: {metrics.get('metrics/mAP50-95(B)', 0):.3f}")
                print(f"   - Precision: {metrics.get('metrics/precision(B)', 0):.3f}")
                print(f"   - Recall: {metrics.get('metrics/recall(B)', 0):.3f}")
            
        except Exception as e:
            print(f"   ⚠️ 验证过程中出现错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🌸 FlowerCount-YOLO 完整训练系统")
    print("=" * 60)
    
    success = main()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 FlowerCount-YOLO 训练完成成功！")
        print("📁 检查 runs/train/flowercount_complete 目录查看结果")
        print("🏆 最佳模型: runs/train/flowercount_complete/weights/best.pt")
        print("📊 训练信息: training_complete_info.json")
    else:
        print("\n" + "=" * 60)
        print("❌ FlowerCount-YOLO 训练失败！")
        print("请检查错误信息并重试")
    
    print("🌸 感谢使用FlowerCount-YOLO系统！")
