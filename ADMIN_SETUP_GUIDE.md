# 🔥 FlowerCount-YOLO 管理员权限安装指南

## 🚀 快速开始（推荐）

### 方法1：使用批处理文件（最简单）

1. **右键点击** `install_cuda_admin.bat`
2. **选择** "以管理员身份运行"
3. **等待安装完成**（约10-20分钟）
4. **保持终端打开**，然后运行项目

### 方法2：使用PowerShell（推荐）

1. **右键点击** PowerShell 图标
2. **选择** "以管理员身份运行"
3. **导航到项目目录**：
   ```powershell
   cd "d:\Code\YOLOv10_Project"
   ```
4. **运行安装脚本**：
   ```powershell
   .\install_cuda_admin.ps1
   ```

### 方法3：手动命令行安装

1. **以管理员身份打开** Anaconda Prompt 或 CMD
2. **运行以下命令**：

```bash
# 创建CUDA环境
conda create -n flowercount-yolo-cuda python=3.9 -y

# 激活环境
conda activate flowercount-yolo-cuda

# 安装PyTorch CUDA版本
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y

# 验证CUDA安装
python -c "import torch; print('CUDA available:', torch.cuda.is_available())"

# 安装项目依赖
pip install ultralytics opencv-python matplotlib seaborn pandas numpy scipy scikit-learn tqdm pyyaml pillow

# 安装可选包
pip install wandb tensorboard grad-cam transformers
```

## 🧪 验证安装

安装完成后，运行以下命令验证：

```python
python -c "
import torch
print('PyTorch version:', torch.__version__)
print('CUDA available:', torch.cuda.is_available())
print('CUDA version:', torch.version.cuda)
print('GPU count:', torch.cuda.device_count())
print('GPU name:', torch.cuda.get_device_name(0))
"
```

**期望输出：**
```
PyTorch version: 2.x.x+cu118
CUDA available: True
CUDA version: 11.8
GPU count: 1
GPU name: [您的GPU名称]
```

## 🚀 运行项目

安装成功后，您可以：

### 1. 运行演示模式
```bash
python quick_start.py --mode demo
```

### 2. 开始训练
```bash
python quick_start.py --mode train
```

### 3. 完整实验流程
```bash
python run_complete_experiment.py
```

### 4. 交互式启动
```bash
python start_project.py
```

## 🔧 常见问题解决

### 问题1：权限被拒绝
**解决方案：**
- 确保以管理员身份运行
- 关闭杀毒软件的实时保护
- 检查Windows用户账户控制(UAC)设置

### 问题2：CUDA不可用
**解决方案：**
```bash
# 检查NVIDIA驱动
nvidia-smi

# 重新安装PyTorch CUDA版本
pip uninstall torch torchvision torchaudio
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 问题3：网络连接问题
**解决方案：**
- 使用国内镜像源：
```bash
pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 问题4：环境冲突
**解决方案：**
```bash
# 删除旧环境
conda env remove -n flowercount-yolo-cuda

# 重新创建
conda create -n flowercount-yolo-cuda python=3.9 -y
```

## 📊 系统要求

### 最低要求：
- **操作系统**: Windows 10/11
- **Python**: 3.8+
- **GPU**: NVIDIA GPU with CUDA Compute Capability 3.5+
- **显存**: 4GB+
- **内存**: 8GB+
- **存储**: 10GB+ 可用空间

### 推荐配置：
- **GPU**: RTX 3060/4060 或更高
- **显存**: 8GB+
- **内存**: 16GB+
- **存储**: SSD 20GB+

## 🎯 下一步

安装完成后：

1. **准备数据**: 将花朵图像放入 `data/` 目录
2. **运行演示**: `python quick_start.py --mode demo`
3. **开始训练**: `python quick_start.py --mode train`
4. **查看结果**: 检查 `results/` 目录

## 📞 技术支持

如果遇到问题：

1. **查看日志**: 检查 `project_startup.log`
2. **验证环境**: 运行验证脚本
3. **重新安装**: 删除环境后重新创建
4. **检查硬件**: 确认GPU和驱动正常

---

**🌸 祝您使用愉快！FlowerCount-YOLO 将为您的花朵检测研究提供强大支持！**
