#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CPU训练脚本 - FlowerCount-YOLO 最终版本
"""

import os
import sys
import time
import json
from pathlib import Path
import torch
from ultralytics import YOLO

def main():
    print("🌸 FlowerCount-YOLO CPU训练 - 最终版本")
    print("=" * 60)
    
    # 检查环境
    print("🔍 检查训练环境...")
    print(f"   Python版本: {sys.version}")
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    print(f"   使用设备: CPU")
    
    # 检查数据集
    print("\n📊 检查数据集...")
    dataset_config = Path("dataset/dataset.yaml")
    if not dataset_config.exists():
        print("❌ 数据集配置文件不存在")
        return False
    
    print(f"   ✅ 数据集配置: {dataset_config}")
    
    # 检查图像数量
    train_dir = Path("dataset/train/images")
    val_dir = Path("dataset/val/images")
    
    if not train_dir.exists() or not val_dir.exists():
        print("❌ 训练或验证目录不存在")
        return False
    
    train_images = list(train_dir.glob("*.jpg"))
    val_images = list(val_dir.glob("*.jpg"))
    
    print(f"   训练图像: {len(train_images)} 张")
    print(f"   验证图像: {len(val_images)} 张")
    
    if len(train_images) == 0:
        print("❌ 没有找到训练图像")
        return False
    
    # 创建YOLO模型
    print("\n🎯 创建YOLO模型...")
    try:
        model = YOLO('yolov8n.pt')
        print("   ✅ 模型创建成功")
    except Exception as e:
        print(f"   ❌ 模型创建失败: {e}")
        return False
    
    # 开始训练
    print("\n🚀 开始CPU训练...")
    print("   训练参数:")
    print(f"   - 数据集: {dataset_config}")
    print(f"   - 轮数: 25")
    print(f"   - 图像尺寸: 640")
    print(f"   - 批次大小: 2 (CPU优化)")
    print(f"   - 设备: CPU")
    print(f"   - 工作进程: 2")
    print(f"   - 学习率: 0.01")
    
    start_time = time.time()
    
    try:
        # 训练模型 - CPU优化参数
        results = model.train(
            data=str(dataset_config),
            epochs=25,
            imgsz=640,
            batch=2,  # 小批次适合CPU
            device='cpu',  # 明确指定CPU
            workers=2,  # 少量工作进程
            patience=10,
            save=True,
            val=True,
            plots=True,
            verbose=True,
            project='runs/train',
            name='flowercount_cpu_final',
            exist_ok=True,
            # 优化参数
            lr0=0.01,
            lrf=0.01,
            momentum=0.937,
            weight_decay=0.0005,
            warmup_epochs=3,
            warmup_momentum=0.8,
            warmup_bias_lr=0.1,
            # 损失权重
            box=7.5,
            cls=0.5,
            dfl=1.5,
            # 数据增强 - 适度减少以节省CPU计算
            hsv_h=0.01,
            hsv_s=0.5,
            hsv_v=0.3,
            degrees=0.0,
            translate=0.05,
            scale=0.3,
            shear=0.0,
            perspective=0.0,
            flipud=0.0,
            fliplr=0.5,
            mosaic=0.8,  # 减少mosaic增强
            mixup=0.0,
            copy_paste=0.0,
            # CPU优化
            amp=False,  # 关闭混合精度
            cache=False  # 关闭缓存
        )
        
        training_time = time.time() - start_time
        
        print(f"\n🎉 训练完成！")
        print(f"   训练时间: {training_time/60:.1f} 分钟")
        print(f"   模型保存目录: {results.save_dir}")
        
        # 保存训练信息
        train_info = {
            'model_path': str(results.save_dir / 'weights' / 'best.pt'),
            'last_model_path': str(results.save_dir / 'weights' / 'last.pt'),
            'results_dir': str(results.save_dir),
            'training_time_minutes': training_time / 60,
            'train_images': len(train_images),
            'val_images': len(val_images),
            'epochs': 25,
            'batch_size': 2,
            'image_size': 640,
            'device': 'CPU',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'project_name': 'FlowerCount-YOLO',
            'version': 'v1.0.0'
        }
        
        with open('training_final_info.json', 'w', encoding='utf-8') as f:
            json.dump(train_info, f, indent=2, ensure_ascii=False)
        
        print("   ✅ 训练信息已保存到 training_final_info.json")
        
        # 运行验证
        print("\n📈 运行最终验证...")
        try:
            best_model = YOLO(str(results.save_dir / 'weights' / 'best.pt'))
            val_results = best_model.val(data=str(dataset_config), device='cpu')
            
            print(f"   ✅ 最终验证完成")
            
        except Exception as e:
            print(f"   ⚠️ 验证过程中出现错误: {e}")
        
        # 检查训练结果
        results_csv = results.save_dir / 'results.csv'
        if results_csv.exists():
            print(f"\n📊 训练结果文件: {results_csv}")
            
            # 读取最后几行结果
            try:
                with open(results_csv, 'r') as f:
                    lines = f.readlines()
                if len(lines) > 1:
                    print("   最后几个epoch的结果:")
                    for line in lines[-3:]:
                        if line.strip():
                            print(f"   {line.strip()}")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🌸 FlowerCount-YOLO CPU训练系统 - 最终版本")
    print("=" * 60)
    
    success = main()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 FlowerCount-YOLO CPU训练完成成功！")
        print("📁 检查 runs/train/flowercount_cpu_final 目录查看结果")
        print("🏆 最佳模型: runs/train/flowercount_cpu_final/weights/best.pt")
        print("🏆 最终模型: runs/train/flowercount_cpu_final/weights/last.pt")
        print("📊 训练信息: training_final_info.json")
        print("📈 训练曲线: runs/train/flowercount_cpu_final/results.png")
        print("🎯 混淆矩阵: runs/train/flowercount_cpu_final/confusion_matrix.png")
    else:
        print("\n" + "=" * 60)
        print("❌ FlowerCount-YOLO CPU训练失败！")
        print("请检查错误信息并重试")
    
    print("\n🌸 FlowerCount-YOLO项目完成！")
    print("   这是一个完整的高密度花朵检测与计数系统")
    print("   包含自动标注、数据集分割、模型训练等完整流程")
    print("   感谢使用FlowerCount-YOLO系统！")
