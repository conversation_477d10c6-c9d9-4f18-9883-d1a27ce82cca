"""
Model Architecture Visualization for FlowerCount-YOLO

This module creates publication-quality diagrams of the FlowerCount-YOLO architecture
including all innovation components for the research paper.

Features:
1. Complete model architecture diagram
2. CBAM attention mechanism detail
3. Multi-scale fusion visualization
4. Density estimation branch
5. Progressive training strategy illustration
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np
from pathlib import Path
import seaborn as sns


class ModelArchitectureVisualizer:
    """
    Creates comprehensive architecture diagrams for FlowerCount-YOLO
    """
    
    def __init__(self, style='paper'):
        """
        Initialize visualizer
        
        Args:
            style: Visualization style ('paper', 'presentation', 'poster')
        """
        self.style = style
        self.colors = self._setup_colors()
        self.fonts = self._setup_fonts()
        
        # Set matplotlib style
        plt.style.use('seaborn-v0_8-whitegrid' if style == 'paper' else 'default')
        
    def _setup_colors(self) -> dict:
        """Setup color scheme"""
        
        if self.style == 'paper':
            # Professional colors for paper
            return {
                'backbone': '#2E86AB',      # Blue
                'neck': '#A23B72',         # Purple
                'head': '#F18F01',         # Orange
                'attention': '#C73E1D',    # Red
                'fusion': '#4CAF50',       # Green
                'density': '#FF9800',      # Amber
                'input': '#9E9E9E',        # Gray
                'output': '#795548',       # Brown
                'flow': '#424242'          # Dark gray
            }
        else:
            # Vibrant colors for presentations
            return {
                'backbone': '#1f77b4',
                'neck': '#ff7f0e', 
                'head': '#2ca02c',
                'attention': '#d62728',
                'fusion': '#9467bd',
                'density': '#8c564b',
                'input': '#e377c2',
                'output': '#7f7f7f',
                'flow': '#17becf'
            }
    
    def _setup_fonts(self) -> dict:
        """Setup font configuration"""
        
        if self.style == 'paper':
            return {
                'title': {'size': 14, 'weight': 'bold'},
                'subtitle': {'size': 12, 'weight': 'bold'},
                'label': {'size': 10, 'weight': 'normal'},
                'annotation': {'size': 8, 'weight': 'normal'}
            }
        else:
            return {
                'title': {'size': 16, 'weight': 'bold'},
                'subtitle': {'size': 14, 'weight': 'bold'},
                'label': {'size': 12, 'weight': 'normal'},
                'annotation': {'size': 10, 'weight': 'normal'}
            }
    
    def draw_complete_architecture(self, save_path: str = None) -> plt.Figure:
        """
        Draw the complete FlowerCount-YOLO architecture
        
        Args:
            save_path: Path to save the figure
            
        Returns:
            matplotlib Figure object
        """
        
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        ax.set_xlim(0, 16)
        ax.set_ylim(0, 10)
        ax.axis('off')
        
        # Title
        ax.text(8, 9.5, 'FlowerCount-YOLO Architecture', 
               ha='center', va='center', **self.fonts['title'])
        
        # Input
        self._draw_input_block(ax, 0.5, 7, 1.5, 1)
        
        # Backbone (YOLOv10)
        self._draw_backbone(ax, 2.5, 6, 3, 3)
        
        # Multi-scale Feature Extraction
        self._draw_multiscale_features(ax, 6, 6, 2, 3)
        
        # CBAM Attention Modules
        self._draw_attention_modules(ax, 8.5, 6, 2, 3)
        
        # Multi-scale Fusion (FPN + PAN)
        self._draw_fusion_module(ax, 11, 6, 2, 3)
        
        # Detection Head
        self._draw_detection_head(ax, 13.5, 7.5, 2, 1)
        
        # Density Estimation Head
        self._draw_density_head(ax, 13.5, 5.5, 2, 1)
        
        # Outputs
        self._draw_outputs(ax, 13.5, 3, 2, 1.5)
        
        # Draw connections
        self._draw_connections(ax)
        
        # Add innovation highlights
        self._add_innovation_highlights(ax)
        
        # Add legend
        self._add_legend(ax)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
        
        return fig
    
    def draw_cbam_detail(self, save_path: str = None) -> plt.Figure:
        """
        Draw detailed CBAM attention mechanism
        
        Args:
            save_path: Path to save the figure
            
        Returns:
            matplotlib Figure object
        """
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.set_xlim(0, 12)
        ax.set_ylim(0, 8)
        ax.axis('off')
        
        # Title
        ax.text(6, 7.5, 'CBAM: Convolutional Block Attention Module', 
               ha='center', va='center', **self.fonts['title'])
        
        # Input feature map
        self._draw_feature_map(ax, 1, 5, 1.5, 1, 'Input\nFeatures', self.colors['input'])
        
        # Channel Attention
        self._draw_channel_attention(ax, 3.5, 6, 2, 1.5)
        
        # Spatial Attention  
        self._draw_spatial_attention(ax, 3.5, 3.5, 2, 1.5)
        
        # Output
        self._draw_feature_map(ax, 9, 5, 1.5, 1, 'Enhanced\nFeatures', self.colors['attention'])
        
        # Draw CBAM connections
        self._draw_cbam_connections(ax)
        
        # Add mathematical formulations
        self._add_cbam_formulas(ax)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
        
        return fig
    
    def draw_progressive_training(self, save_path: str = None) -> plt.Figure:
        """
        Draw progressive training strategy
        
        Args:
            save_path: Path to save the figure
            
        Returns:
            matplotlib Figure object
        """
        
        fig, ax = plt.subplots(1, 1, figsize=(14, 6))
        ax.set_xlim(0, 14)
        ax.set_ylim(0, 6)
        ax.axis('off')
        
        # Title
        ax.text(7, 5.5, 'Progressive Training Strategy', 
               ha='center', va='center', **self.fonts['title'])
        
        # Stage 1: Warmup
        self._draw_training_stage(ax, 1, 3, 3, 1.5, 'Stage 1: Warmup\n(10 epochs)', 
                                'Freeze backbone\nDetection only', self.colors['backbone'])
        
        # Stage 2: Detection Training
        self._draw_training_stage(ax, 5.5, 3, 3, 1.5, 'Stage 2: Detection\n(100 epochs)', 
                                'Full detection\nCounting loss', self.colors['head'])
        
        # Stage 3: Full Training
        self._draw_training_stage(ax, 10, 3, 3, 1.5, 'Stage 3: Full\n(190 epochs)', 
                                'All components\nDensity estimation', self.colors['density'])
        
        # Draw progression arrows
        self._draw_progression_arrows(ax)
        
        # Add loss weight information
        self._add_loss_weights(ax)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
        
        return fig
    
    def draw_multiscale_fusion(self, save_path: str = None) -> plt.Figure:
        """
        Draw multi-scale fusion mechanism
        
        Args:
            save_path: Path to save the figure
            
        Returns:
            matplotlib Figure object
        """
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.set_xlim(0, 12)
        ax.set_ylim(0, 8)
        ax.axis('off')
        
        # Title
        ax.text(6, 7.5, 'Multi-Scale Feature Fusion (FPN + PAN)', 
               ha='center', va='center', **self.fonts['title'])
        
        # Draw FPN (top-down)
        self._draw_fpn_pathway(ax, 2, 5.5, 8, 1.5)
        
        # Draw PAN (bottom-up)
        self._draw_pan_pathway(ax, 2, 2, 8, 1.5)
        
        # Feature levels
        levels = ['P3\n(High Res)', 'P4\n(Medium Res)', 'P5\n(Low Res)']
        for i, level in enumerate(levels):
            x = 2.5 + i * 2.5
            self._draw_feature_map(ax, x, 4, 1, 0.8, level, self.colors['fusion'])
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
        
        return fig
    
    def _draw_input_block(self, ax, x, y, w, h):
        """Draw input block"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['input'], alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'Input Image\n640×640×3', 
               ha='center', va='center', **self.fonts['label'])
    
    def _draw_backbone(self, ax, x, y, w, h):
        """Draw backbone network"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['backbone'], alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'YOLOv10\nBackbone\n(CSPDarknet)', 
               ha='center', va='center', **self.fonts['label'], color='white')
    
    def _draw_multiscale_features(self, ax, x, y, w, h):
        """Draw multi-scale feature extraction"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['neck'], alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'Multi-Scale\nFeatures\nP3, P4, P5', 
               ha='center', va='center', **self.fonts['label'], color='white')
    
    def _draw_attention_modules(self, ax, x, y, w, h):
        """Draw attention modules"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['attention'], alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'CBAM\nAttention\nModules', 
               ha='center', va='center', **self.fonts['label'], color='white')
    
    def _draw_fusion_module(self, ax, x, y, w, h):
        """Draw fusion module"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['fusion'], alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'Feature\nFusion\n(FPN+PAN)', 
               ha='center', va='center', **self.fonts['label'], color='white')
    
    def _draw_detection_head(self, ax, x, y, w, h):
        """Draw detection head"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['head'], alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'Detection\nHead', 
               ha='center', va='center', **self.fonts['label'], color='white')
    
    def _draw_density_head(self, ax, x, y, w, h):
        """Draw density estimation head"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['density'], alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'Density\nHead', 
               ha='center', va='center', **self.fonts['label'], color='white')
    
    def _draw_outputs(self, ax, x, y, w, h):
        """Draw outputs"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['output'], alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'Outputs:\n• Bounding Boxes\n• Flower Count\n• Density Map', 
               ha='center', va='center', **self.fonts['label'], color='white')
    
    def _draw_connections(self, ax):
        """Draw connections between modules"""
        connections = [
            ((2, 7.5), (2.5, 7.5)),      # Input to Backbone
            ((5.5, 7.5), (6, 7.5)),      # Backbone to Features
            ((8, 7.5), (8.5, 7.5)),      # Features to Attention
            ((10.5, 7.5), (11, 7.5)),    # Attention to Fusion
            ((13, 7.5), (13.5, 8)),      # Fusion to Detection
            ((13, 7.5), (13.5, 6)),      # Fusion to Density
            ((13.5, 7), (13.5, 4.5)),    # Heads to Output
        ]
        
        for start, end in connections:
            arrow = ConnectionPatch(start, end, "data", "data",
                                  arrowstyle="->", shrinkA=5, shrinkB=5,
                                  mutation_scale=20, fc=self.colors['flow'])
            ax.add_patch(arrow)
    
    def _draw_feature_map(self, ax, x, y, w, h, label, color):
        """Draw a feature map representation"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.05",
                             facecolor=color, alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, label, ha='center', va='center', 
               **self.fonts['label'], color='white' if color != self.colors['input'] else 'black')
    
    def _draw_channel_attention(self, ax, x, y, w, h):
        """Draw channel attention mechanism"""
        # Main block
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['attention'], alpha=0.5)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'Channel\nAttention', 
               ha='center', va='center', **self.fonts['label'])
        
        # Add GAP and GMP
        ax.text(x + w/2, y + h + 0.2, 'GAP + GMP → FC → Sigmoid', 
               ha='center', va='center', **self.fonts['annotation'])
    
    def _draw_spatial_attention(self, ax, x, y, w, h):
        """Draw spatial attention mechanism"""
        # Main block
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['attention'], alpha=0.5)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2, 'Spatial\nAttention', 
               ha='center', va='center', **self.fonts['label'])
        
        # Add operation description
        ax.text(x + w/2, y - 0.3, 'Concat → Conv → Sigmoid', 
               ha='center', va='center', **self.fonts['annotation'])
    
    def _draw_cbam_connections(self, ax):
        """Draw CBAM internal connections"""
        # Input to channel attention
        arrow1 = ConnectionPatch((2.5, 5.5), (3.5, 6.5), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5)
        ax.add_patch(arrow1)
        
        # Channel attention to spatial attention
        arrow2 = ConnectionPatch((4.5, 6), (4.5, 5), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5)
        ax.add_patch(arrow2)
        
        # Spatial attention to output
        arrow3 = ConnectionPatch((5.5, 4.5), (9, 5.5), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5)
        ax.add_patch(arrow3)
    
    def _draw_training_stage(self, ax, x, y, w, h, title, description, color):
        """Draw a training stage"""
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=color, alpha=0.7)
        ax.add_patch(rect)
        ax.text(x + w/2, y + h/2 + 0.3, title, 
               ha='center', va='center', **self.fonts['subtitle'], color='white')
        ax.text(x + w/2, y + h/2 - 0.3, description, 
               ha='center', va='center', **self.fonts['annotation'], color='white')
    
    def _draw_progression_arrows(self, ax):
        """Draw progression arrows between training stages"""
        arrow1 = ConnectionPatch((4, 3.75), (5.5, 3.75), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=20, fc='black')
        ax.add_patch(arrow1)
        
        arrow2 = ConnectionPatch((8.5, 3.75), (10, 3.75), "data", "data",
                               arrowstyle="->", shrinkA=5, shrinkB=5,
                               mutation_scale=20, fc='black')
        ax.add_patch(arrow2)
    
    def _draw_fpn_pathway(self, ax, x, y, w, h):
        """Draw FPN pathway"""
        ax.text(x + w/2, y + h + 0.2, 'FPN (Top-Down)', 
               ha='center', va='center', **self.fonts['subtitle'])
        
        # Draw pathway
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['fusion'], alpha=0.3)
        ax.add_patch(rect)
    
    def _draw_pan_pathway(self, ax, x, y, w, h):
        """Draw PAN pathway"""
        ax.text(x + w/2, y - 0.3, 'PAN (Bottom-Up)', 
               ha='center', va='center', **self.fonts['subtitle'])
        
        # Draw pathway
        rect = FancyBboxPatch((x, y), w, h, boxstyle="round,pad=0.1",
                             facecolor=self.colors['fusion'], alpha=0.3)
        ax.add_patch(rect)
    
    def _add_innovation_highlights(self, ax):
        """Add innovation highlights"""
        # Innovation callouts
        innovations = [
            (8.5, 9, "Innovation 1:\nCBAM Attention"),
            (11, 9, "Innovation 2:\nMulti-Scale Fusion"),
            (13.5, 9, "Innovation 3:\nDensity Estimation")
        ]
        
        for x, y, text in innovations:
            ax.text(x, y, text, ha='center', va='center', 
                   **self.fonts['annotation'], 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.7))
    
    def _add_legend(self, ax):
        """Add legend"""
        legend_elements = [
            patches.Patch(color=self.colors['backbone'], label='Backbone'),
            patches.Patch(color=self.colors['attention'], label='Attention'),
            patches.Patch(color=self.colors['fusion'], label='Fusion'),
            patches.Patch(color=self.colors['head'], label='Detection'),
            patches.Patch(color=self.colors['density'], label='Density')
        ]
        
        ax.legend(handles=legend_elements, loc='upper left', 
                 bbox_to_anchor=(0, 1), **self.fonts['annotation'])
    
    def _add_cbam_formulas(self, ax):
        """Add CBAM mathematical formulations"""
        formulas = [
            "Channel: Mc = σ(MLP(AvgPool(F)) + MLP(MaxPool(F)))",
            "Spatial: Ms = σ(Conv([AvgPool(F); MaxPool(F)]))",
            "Output: F' = Ms(F) ⊗ Mc(F) ⊗ F"
        ]
        
        for i, formula in enumerate(formulas):
            ax.text(6, 1.5 - i*0.4, formula, ha='center', va='center',
                   **self.fonts['annotation'], 
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='lightblue', alpha=0.5))
    
    def _add_loss_weights(self, ax):
        """Add loss weight information"""
        weights = [
            "Stage 1: Detection=1.0, Counting=0.0, Density=0.0",
            "Stage 2: Detection=1.0, Counting=0.3, Density=0.0", 
            "Stage 3: Detection=1.0, Counting=0.5, Density=0.3"
        ]
        
        for i, weight in enumerate(weights):
            x = 2.5 + i * 4.5
            ax.text(x, 1.5, weight, ha='center', va='center',
                   **self.fonts['annotation'],
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='lightgreen', alpha=0.5))
    
    def generate_all_diagrams(self, output_dir: str):
        """Generate all architecture diagrams"""
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Complete architecture
        self.draw_complete_architecture(str(output_path / 'complete_architecture.png'))
        
        # CBAM detail
        self.draw_cbam_detail(str(output_path / 'cbam_detail.png'))
        
        # Progressive training
        self.draw_progressive_training(str(output_path / 'progressive_training.png'))
        
        # Multi-scale fusion
        self.draw_multiscale_fusion(str(output_path / 'multiscale_fusion.png'))
        
        print(f"All architecture diagrams saved to: {output_path}")


def main():
    """Generate all model architecture diagrams"""
    
    # Initialize visualizer
    visualizer = ModelArchitectureVisualizer(style='paper')
    
    # Generate all diagrams
    visualizer.generate_all_diagrams('results/architecture_diagrams')
    
    print("Model architecture diagrams generated successfully!")


if __name__ == "__main__":
    main()
