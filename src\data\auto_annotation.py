"""
Automatic Annotation System for Flower Detection

This module implements a sophisticated auto-annotation pipeline using:
1. SAM2 (Segment Anything Model 2) for precise segmentation
2. CLIP for zero-shot flower classification
3. Advanced post-processing for high-quality annotations

The system generates COCO-format annotations automatically, eliminating
the need for manual labeling while maintaining high annotation quality.
"""

import os
import json
import cv2
import numpy as np
import torch
import torch.nn.functional as F
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import logging
from datetime import datetime
from PIL import Image
import matplotlib.pyplot as plt

# Foundation model imports
try:
    from segment_anything import sam_model_registry, SamPredictor
    from transformers import CLIPProcessor, CLIPModel
    SAM_AVAILABLE = True
    CLIP_AVAILABLE = True
except ImportError:
    logging.warning("Foundation models not available. Install segment-anything and transformers.")
    SAM_AVAILABLE = False
    CLIP_AVAILABLE = False
    # Create dummy classes for compatibility
    class SamPredictor:
        pass
    class CLIPProcessor:
        pass
    class CLIPModel:
        pass


class FlowerAutoAnnotator:
    """
    Advanced automatic annotation system for flower detection
    
    Combines SAM2 for segmentation and CLIP for classification to generate
    high-quality bounding box annotations for flower detection training.
    """
    
    def __init__(self, 
                 sam_checkpoint: str = "sam_vit_h_4b8939.pth",
                 sam_model_type: str = "vit_h",
                 clip_model: str = "openai/clip-vit-base-patch32",
                 device: str = "auto"):
        
        self.device = self._setup_device(device)
        self.logger = self._setup_logger()
        
        # Initialize models
        self.sam_predictor = self._load_sam_model(sam_checkpoint, sam_model_type)
        self.clip_model, self.clip_processor = self._load_clip_model(clip_model)
        
        # Flower detection prompts for CLIP
        self.flower_prompts = [
            "a photo of a flower",
            "a beautiful flower in bloom", 
            "colorful flower petals",
            "a flowering plant",
            "garden flowers",
            "wild flowers in nature"
        ]
        
        self.non_flower_prompts = [
            "a photo of leaves",
            "green foliage",
            "tree branches",
            "grass and vegetation",
            "background scenery"
        ]
        
        # Annotation parameters
        self.confidence_threshold = 0.7
        self.nms_threshold = 0.5
        self.min_area = 100  # Minimum bounding box area
        self.max_area = 50000  # Maximum bounding box area
        
    def _setup_device(self, device: str) -> torch.device:
        """Setup computation device"""
        if device == "auto":
            if torch.cuda.is_available():
                return torch.device("cuda")
            elif torch.backends.mps.is_available():
                return torch.device("mps")
            else:
                return torch.device("cpu")
        return torch.device(device)
    
    def _setup_logger(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger("FlowerAutoAnnotator")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_sam_model(self, checkpoint: str, model_type: str) -> Optional[SamPredictor]:
        """Load SAM model for segmentation"""
        if not SAM_AVAILABLE:
            self.logger.warning("SAM not available, using fallback annotation")
            return None
        try:
            sam = sam_model_registry[model_type](checkpoint=checkpoint)
            sam.to(device=self.device)
            predictor = SamPredictor(sam)
            self.logger.info(f"SAM model loaded successfully on {self.device}")
            return predictor
        except Exception as e:
            self.logger.error(f"Failed to load SAM model: {e}")
            return None
    
    def _load_clip_model(self, model_name: str) -> Tuple[Optional[CLIPModel], Optional[CLIPProcessor]]:
        """Load CLIP model for classification"""
        if not CLIP_AVAILABLE:
            self.logger.warning("CLIP not available, using fallback classification")
            return None, None
        try:
            model = CLIPModel.from_pretrained(model_name).to(self.device)
            processor = CLIPProcessor.from_pretrained(model_name)
            self.logger.info(f"CLIP model loaded successfully on {self.device}")
            return model, processor
        except Exception as e:
            self.logger.error(f"Failed to load CLIP model: {e}")
            return None, None
    
    def generate_sam_proposals(self, image: np.ndarray) -> List[Dict]:
        """
        Generate object proposals using SAM
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of proposal dictionaries with masks and scores
        """
        if self.sam_predictor is None:
            return []
        
        # Set image for SAM
        self.sam_predictor.set_image(image)
        
        # Generate automatic masks
        try:
            # Use automatic mask generation
            from segment_anything import SamAutomaticMaskGenerator
            mask_generator = SamAutomaticMaskGenerator(
                model=self.sam_predictor.model,
                points_per_side=32,
                pred_iou_thresh=0.8,
                stability_score_thresh=0.9,
                crop_n_layers=1,
                crop_n_points_downscale_factor=2,
                min_mask_region_area=100,
            )
            
            masks = mask_generator.generate(image)
            
            # Convert to our format
            proposals = []
            for mask_data in masks:
                proposals.append({
                    'mask': mask_data['segmentation'],
                    'score': mask_data['stability_score'],
                    'area': mask_data['area'],
                    'bbox': mask_data['bbox']  # [x, y, w, h]
                })
            
            return proposals
            
        except Exception as e:
            self.logger.error(f"SAM proposal generation failed: {e}")
            return []
    
    def classify_proposals_with_clip(self, 
                                   image: np.ndarray, 
                                   proposals: List[Dict]) -> List[Dict]:
        """
        Classify proposals using CLIP to identify flowers
        
        Args:
            image: Original image
            proposals: List of SAM proposals
            
        Returns:
            Filtered proposals with flower classification scores
        """
        if self.clip_model is None or self.clip_processor is None:
            return proposals
        
        flower_proposals = []
        
        for proposal in proposals:
            try:
                # Extract region of interest
                bbox = proposal['bbox']
                x, y, w, h = bbox
                roi = image[y:y+h, x:x+w]
                
                if roi.size == 0:
                    continue
                
                # Convert to PIL Image
                roi_pil = Image.fromarray(cv2.cvtColor(roi, cv2.COLOR_BGR2RGB))
                
                # Prepare inputs for CLIP
                all_prompts = self.flower_prompts + self.non_flower_prompts
                inputs = self.clip_processor(
                    text=all_prompts,
                    images=roi_pil,
                    return_tensors="pt",
                    padding=True
                ).to(self.device)
                
                # Get predictions
                with torch.no_grad():
                    outputs = self.clip_model(**inputs)
                    logits_per_image = outputs.logits_per_image
                    probs = logits_per_image.softmax(dim=1)
                
                # Calculate flower confidence
                flower_prob = probs[0, :len(self.flower_prompts)].sum().item()
                non_flower_prob = probs[0, len(self.flower_prompts):].sum().item()
                
                flower_confidence = flower_prob / (flower_prob + non_flower_prob)
                
                # Filter based on confidence
                if flower_confidence > self.confidence_threshold:
                    proposal['flower_confidence'] = flower_confidence
                    proposal['clip_score'] = flower_confidence
                    flower_proposals.append(proposal)
                    
            except Exception as e:
                self.logger.warning(f"CLIP classification failed for proposal: {e}")
                continue
        
        return flower_proposals
    
    def post_process_annotations(self, proposals: List[Dict]) -> List[Dict]:
        """
        Post-process annotations with NMS and filtering
        
        Args:
            proposals: List of flower proposals
            
        Returns:
            Cleaned and filtered annotations
        """
        if not proposals:
            return []
        
        # Convert to format for NMS
        boxes = []
        scores = []
        
        for proposal in proposals:
            bbox = proposal['bbox']
            x, y, w, h = bbox
            
            # Filter by area
            area = w * h
            if area < self.min_area or area > self.max_area:
                continue
            
            # Convert to [x1, y1, x2, y2] format
            boxes.append([x, y, x + w, y + h])
            scores.append(proposal.get('clip_score', proposal.get('score', 0.5)))
        
        if not boxes:
            return []
        
        # Apply Non-Maximum Suppression
        boxes_tensor = torch.tensor(boxes, dtype=torch.float32)
        scores_tensor = torch.tensor(scores, dtype=torch.float32)
        
        keep_indices = torch.ops.torchvision.nms(
            boxes_tensor, scores_tensor, self.nms_threshold
        )
        
        # Filter proposals based on NMS results
        filtered_proposals = []
        for idx in keep_indices:
            proposal = proposals[idx.item()]
            filtered_proposals.append(proposal)
        
        return filtered_proposals
    
    def annotate_image(self, image_path: str) -> Dict:
        """
        Generate annotations for a single image
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Annotation dictionary in COCO format
        """
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            self.logger.error(f"Failed to load image: {image_path}")
            return {}
        
        height, width = image.shape[:2]
        
        # Generate SAM proposals
        self.logger.info(f"Generating proposals for {os.path.basename(image_path)}")
        proposals = self.generate_sam_proposals(image)
        
        if not proposals:
            self.logger.warning(f"No proposals generated for {image_path}")
            return self._create_empty_annotation(image_path, width, height)
        
        # Classify with CLIP
        self.logger.info(f"Classifying {len(proposals)} proposals")
        flower_proposals = self.classify_proposals_with_clip(image, proposals)
        
        # Post-process
        final_proposals = self.post_process_annotations(flower_proposals)
        
        # Convert to COCO format
        annotation = self._create_coco_annotation(
            image_path, width, height, final_proposals
        )
        
        self.logger.info(
            f"Generated {len(final_proposals)} annotations for {os.path.basename(image_path)}"
        )
        
        return annotation
    
    def _create_empty_annotation(self, image_path: str, width: int, height: int) -> Dict:
        """Create empty annotation for images with no detections"""
        return {
            'image': {
                'file_name': os.path.basename(image_path),
                'width': width,
                'height': height,
                'id': hash(image_path) % (10**8)
            },
            'annotations': []
        }
    
    def _create_coco_annotation(self, 
                               image_path: str, 
                               width: int, 
                               height: int, 
                               proposals: List[Dict]) -> Dict:
        """Convert proposals to COCO annotation format"""
        
        image_id = hash(image_path) % (10**8)
        
        annotations = []
        for i, proposal in enumerate(proposals):
            bbox = proposal['bbox']
            x, y, w, h = bbox
            
            # Ensure bbox is within image bounds
            x = max(0, min(x, width - 1))
            y = max(0, min(y, height - 1))
            w = min(w, width - x)
            h = min(h, height - y)
            
            annotation = {
                'id': image_id * 1000 + i,
                'image_id': image_id,
                'category_id': 1,  # Flower class
                'bbox': [x, y, w, h],
                'area': w * h,
                'iscrowd': 0,
                'confidence': proposal.get('clip_score', 0.5)
            }
            annotations.append(annotation)
        
        return {
            'image': {
                'file_name': os.path.basename(image_path),
                'width': width,
                'height': height,
                'id': image_id
            },
            'annotations': annotations
        }
    
    def annotate_dataset(self, 
                        image_dir: str, 
                        output_path: str,
                        visualize: bool = True) -> Dict:
        """
        Annotate entire dataset
        
        Args:
            image_dir: Directory containing images
            output_path: Path to save COCO annotations
            visualize: Whether to save visualization images
            
        Returns:
            Complete COCO dataset dictionary
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_paths = []
        
        # Collect all image paths
        for ext in image_extensions:
            image_paths.extend(Path(image_dir).glob(f'*{ext}'))
            image_paths.extend(Path(image_dir).glob(f'*{ext.upper()}'))
        
        if not image_paths:
            self.logger.error(f"No images found in {image_dir}")
            return {}
        
        self.logger.info(f"Found {len(image_paths)} images to annotate")
        
        # Initialize COCO dataset structure
        coco_dataset = {
            'info': {
                'description': 'Auto-annotated Flower Dataset',
                'version': '1.0',
                'year': datetime.now().year,
                'contributor': 'FlowerCount-YOLO Auto-Annotator',
                'date_created': datetime.now().isoformat()
            },
            'licenses': [],
            'images': [],
            'annotations': [],
            'categories': [
                {
                    'id': 1,
                    'name': 'flower',
                    'supercategory': 'plant'
                }
            ]
        }
        
        # Process each image
        total_annotations = 0
        
        for i, image_path in enumerate(image_paths):
            self.logger.info(f"Processing {i+1}/{len(image_paths)}: {image_path.name}")
            
            try:
                annotation_data = self.annotate_image(str(image_path))
                
                if annotation_data:
                    coco_dataset['images'].append(annotation_data['image'])
                    coco_dataset['annotations'].extend(annotation_data['annotations'])
                    total_annotations += len(annotation_data['annotations'])
                    
                    # Save visualization if requested
                    if visualize and annotation_data['annotations']:
                        self._save_visualization(str(image_path), annotation_data)
                        
            except Exception as e:
                self.logger.error(f"Failed to process {image_path}: {e}")
                continue
        
        # Save annotations
        with open(output_path, 'w') as f:
            json.dump(coco_dataset, f, indent=2)
        
        self.logger.info(f"Annotation complete! Generated {total_annotations} annotations")
        self.logger.info(f"Annotations saved to: {output_path}")
        
        return coco_dataset
    
    def _save_visualization(self, image_path: str, annotation_data: Dict):
        """Save visualization of annotations"""
        try:
            # Load image
            image = cv2.imread(image_path)
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Draw bounding boxes
            for ann in annotation_data['annotations']:
                bbox = ann['bbox']
                x, y, w, h = bbox
                confidence = ann.get('confidence', 0.0)
                
                # Draw rectangle
                cv2.rectangle(image_rgb, (int(x), int(y)), 
                            (int(x + w), int(y + h)), (255, 0, 0), 2)
                
                # Add confidence text
                cv2.putText(image_rgb, f'{confidence:.2f}', 
                          (int(x), int(y - 5)), cv2.FONT_HERSHEY_SIMPLEX, 
                          0.5, (255, 0, 0), 1)
            
            # Save visualization
            vis_dir = Path(image_path).parent / 'visualizations'
            vis_dir.mkdir(exist_ok=True)
            
            vis_path = vis_dir / f"annotated_{Path(image_path).name}"
            cv2.imwrite(str(vis_path), cv2.cvtColor(image_rgb, cv2.COLOR_RGB2BGR))
            
        except Exception as e:
            self.logger.warning(f"Failed to save visualization for {image_path}: {e}")


def main():
    """Main function for testing auto-annotation"""
    
    # Initialize annotator
    annotator = FlowerAutoAnnotator()
    
    # Annotate dataset
    dataset = annotator.annotate_dataset(
        image_dir="data/",
        output_path="data/annotations.json",
        visualize=True
    )
    
    print(f"Generated annotations for {len(dataset.get('images', []))} images")
    print(f"Total annotations: {len(dataset.get('annotations', []))}")


if __name__ == "__main__":
    main()
