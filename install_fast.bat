@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                   FlowerCount-YOLO 加速安装脚本                             ║
echo ║                        (使用国内镜像源)                                     ║
echo ║                                                                              ║
echo ║    🌸 Advanced Flower Detection and Counting System                         ║
echo ║    🚀 超快速安装，使用最优镜像源                                             ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel__ == 0 (
    echo ✅ 管理员权限确认
) else (
    echo ❌ 需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 🔍 检查conda安装...
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到conda！
    pause
    exit /b 1
)
echo ✅ conda已安装

echo.
echo 🏗️ 创建FlowerCount-YOLO CUDA环境...
conda create -n flowercount-yolo-cuda python=3.9 -y

echo.
echo 🎯 激活环境...
call conda activate flowercount-yolo-cuda

echo.
echo 🚀 配置pip国内镜像源（加速下载）...
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn
echo ✅ 镜像源配置完成

echo.
echo 📦 升级pip...
python -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple/

echo.
echo 🔥 安装PyTorch CUDA版本（使用清华镜像）...
echo 正在下载，请稍候...
pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ 清华镜像失败，尝试阿里云镜像...
    pip install torch torchvision torchaudio -i https://mirrors.aliyun.com/pypi/simple/
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️ 阿里云镜像失败，尝试豆瓣镜像...
        pip install torch torchvision torchaudio -i https://pypi.douban.com/simple/
        if %ERRORLEVEL% NEQ 0 (
            echo ❌ 所有镜像都失败，使用官方源...
            pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
        )
    )
)

echo.
echo 🧪 验证PyTorch CUDA...
python -c "import torch; print('✅ PyTorch version:', torch.__version__); print('🔥 CUDA available:', torch.cuda.is_available())"

echo.
echo 📚 安装核心依赖包（批量安装，加速下载）...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ ultralytics opencv-python opencv-contrib-python matplotlib seaborn pandas numpy scipy scikit-learn tqdm pyyaml pillow
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ 清华镜像失败，尝试其他镜像...
    pip install -i https://mirrors.aliyun.com/pypi/simple/ ultralytics opencv-python opencv-contrib-python matplotlib seaborn pandas numpy scipy scikit-learn tqdm pyyaml pillow
)

echo.
echo 🎨 安装计算机视觉包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ albumentations scikit-image

echo.
echo 📊 安装实验跟踪包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ wandb tensorboard mlflow

echo.
echo 🔍 安装可解释性包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ grad-cam transformers timm

echo.
echo 📏 安装评估包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ pycocotools torchmetrics

echo.
echo 🛠️ 安装工具包...
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ rich hydra-core

echo.
echo 🧪 最终验证...
python -c "
print('🔍 验证FlowerCount-YOLO环境...')
try:
    import torch, torchvision, cv2, numpy as np, matplotlib.pyplot as plt
    import pandas as pd, sklearn, yaml, PIL, tqdm, ultralytics
    print('✅ 所有核心包导入成功!')
    print('🔥 PyTorch CUDA:', torch.cuda.is_available())
    if torch.cuda.is_available():
        print('🎮 GPU:', torch.cuda.get_device_name(0))
        print('💾 GPU内存:', f'{torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB')
    print('🎯 FlowerCount-YOLO环境准备就绪!')
except Exception as e:
    print('❌ 验证失败:', e)
"

echo.
echo 🎉 加速安装完成！
echo.
echo 📋 镜像源已配置为清华大学源，后续安装将自动加速
echo 📋 下一步:
echo    1. 运行: python quick_start.py --mode demo
echo    2. 或运行: python start_project.py
echo.
echo 🌸 FlowerCount-YOLO CUDA环境已准备就绪！
echo.
pause
