#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 演示脚本
展示完整的高密度花朵检测与计数系统
"""

import os
import sys
import json
import time
import random
from pathlib import Path
from typing import List, Dict, <PERSON>ple
import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import seaborn as sns

class FlowerCountYOLODemo:
    """FlowerCount-YOLO演示系统"""
    
    def __init__(self):
        """初始化演示系统"""
        self.project_root = Path.cwd()
        self.data_dir = self.project_root / "data"
        self.labels_dir = self.data_dir / "labels"
        self.viz_dir = self.project_root / "annotation_visualizations"
        self.dataset_dir = self.project_root / "dataset"
        
        print("🌸 FlowerCount-YOLO 演示系统")
        print("=" * 50)
        print(f"📁 项目目录: {self.project_root}")
        
        # 检查项目结构
        self.check_project_structure()
    
    def check_project_structure(self):
        """检查项目结构"""
        print("\n🔍 检查项目结构...")
        
        required_dirs = [
            self.data_dir,
            self.labels_dir,
            self.viz_dir,
            self.dataset_dir
        ]
        
        for dir_path in required_dirs:
            if dir_path.exists():
                print(f"   ✅ {dir_path.name}: 存在")
            else:
                print(f"   ❌ {dir_path.name}: 不存在")
        
        # 统计文件数量
        image_files = list(self.data_dir.glob("*.JPG"))
        label_files = list(self.labels_dir.glob("*.txt"))
        viz_files = list(self.viz_dir.glob("*.jpg"))
        
        print(f"\n📊 文件统计:")
        print(f"   🖼️  原始图像: {len(image_files)} 张")
        print(f"   🏷️  标注文件: {len(label_files)} 个")
        print(f"   🎨 可视化文件: {len(viz_files)} 个")
    
    def analyze_dataset_statistics(self) -> Dict:
        """分析数据集统计信息"""
        print("\n📈 分析数据集统计信息...")
        
        # 读取数据集配置
        dataset_config_path = self.dataset_dir / "dataset.yaml"
        split_info_path = self.dataset_dir / "split_info.json"
        
        stats = {}
        
        if split_info_path.exists():
            with open(split_info_path, 'r', encoding='utf-8') as f:
                split_info = json.load(f)
            
            stats = split_info.get('statistics', {})
            print(f"   📊 总图像数: {stats.get('total_images', 0)}")
            print(f"   🌸 总花朵数: {stats.get('total_flowers', 0)}")
            print(f"   📈 平均每图: {stats.get('avg_flowers_per_image', 0):.1f} 朵")
            print(f"   📊 花朵范围: {stats.get('min_flowers', 0)} - {stats.get('max_flowers', 0)} 朵")
        
        return stats
    
    def show_sample_detections(self, num_samples: int = 6):
        """展示样本检测结果"""
        print(f"\n🎨 展示 {num_samples} 个检测样本...")
        
        # 获取可视化文件
        viz_files = list(self.viz_dir.glob("*.jpg"))
        if not viz_files:
            print("   ❌ 未找到可视化文件")
            return
        
        # 随机选择样本
        sample_files = random.sample(viz_files, min(num_samples, len(viz_files)))
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, viz_file in enumerate(sample_files):
            if i >= len(axes):
                break
            
            # 读取图像
            img = cv2.imread(str(viz_file))
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # 显示图像
            axes[i].imshow(img_rgb)
            axes[i].set_title(f"检测结果: {viz_file.stem}", fontsize=12)
            axes[i].axis('off')
            
            # 获取对应的标注信息
            image_name = viz_file.stem.replace('annotated_', '')
            label_file = self.labels_dir / f"{image_name}.txt"
            
            if label_file.exists():
                with open(label_file, 'r') as f:
                    lines = f.readlines()
                flower_count = len([line for line in lines if line.strip()])
                axes[i].text(10, 30, f"花朵数: {flower_count}", 
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                           fontsize=10, color='black')
        
        plt.tight_layout()
        plt.suptitle("FlowerCount-YOLO 检测结果展示", fontsize=16, y=1.02)
        
        # 保存图表
        output_path = self.project_root / "demo_sample_detections.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"   ✅ 样本展示已保存: {output_path}")
    
    def analyze_flower_distribution(self):
        """分析花朵分布"""
        print("\n📊 分析花朵数量分布...")
        
        # 收集所有标注文件的花朵数量
        flower_counts = []
        label_files = list(self.labels_dir.glob("*.txt"))
        
        for label_file in label_files:
            with open(label_file, 'r') as f:
                lines = f.readlines()
            flower_count = len([line for line in lines if line.strip()])
            flower_counts.append(flower_count)
        
        if not flower_counts:
            print("   ❌ 未找到标注文件")
            return
        
        # 创建分布图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 直方图
        axes[0, 0].hist(flower_counts, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('花朵数量分布直方图')
        axes[0, 0].set_xlabel('花朵数量')
        axes[0, 0].set_ylabel('图像数量')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 箱线图
        axes[0, 1].boxplot(flower_counts, vert=True)
        axes[0, 1].set_title('花朵数量箱线图')
        axes[0, 1].set_ylabel('花朵数量')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 累积分布
        sorted_counts = sorted(flower_counts)
        cumulative = np.arange(1, len(sorted_counts) + 1) / len(sorted_counts)
        axes[1, 0].plot(sorted_counts, cumulative, 'b-', linewidth=2)
        axes[1, 0].set_title('花朵数量累积分布')
        axes[1, 0].set_xlabel('花朵数量')
        axes[1, 0].set_ylabel('累积概率')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 密度分组
        density_groups = {
            '0朵': len([c for c in flower_counts if c == 0]),
            '1-5朵': len([c for c in flower_counts if 1 <= c <= 5]),
            '6-15朵': len([c for c in flower_counts if 6 <= c <= 15]),
            '16-30朵': len([c for c in flower_counts if 16 <= c <= 30]),
            '31-50朵': len([c for c in flower_counts if 31 <= c <= 50]),
            '50+朵': len([c for c in flower_counts if c > 50])
        }
        
        groups = list(density_groups.keys())
        counts = list(density_groups.values())
        colors = ['lightcoral', 'lightblue', 'lightgreen', 'gold', 'orange', 'red']
        
        axes[1, 1].pie(counts, labels=groups, colors=colors, autopct='%1.1f%%', startangle=90)
        axes[1, 1].set_title('花朵密度分组分布')
        
        plt.tight_layout()
        plt.suptitle("FlowerCount-YOLO 花朵分布分析", fontsize=16, y=1.02)
        
        # 保存图表
        output_path = self.project_root / "demo_flower_distribution.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印统计信息
        print(f"   📊 统计信息:")
        print(f"      总图像数: {len(flower_counts)}")
        print(f"      总花朵数: {sum(flower_counts)}")
        print(f"      平均每图: {np.mean(flower_counts):.1f} 朵")
        print(f"      标准差: {np.std(flower_counts):.1f}")
        print(f"      最小值: {min(flower_counts)} 朵")
        print(f"      最大值: {max(flower_counts)} 朵")
        print(f"      中位数: {np.median(flower_counts):.1f} 朵")
        
        print(f"   📊 密度分组:")
        for group, count in density_groups.items():
            percentage = count / len(flower_counts) * 100
            print(f"      {group}: {count} 张图像 ({percentage:.1f}%)")
        
        print(f"   ✅ 分布分析已保存: {output_path}")
    
    def show_high_density_examples(self, top_n: int = 5):
        """展示高密度检测样例"""
        print(f"\n🔥 展示前 {top_n} 个高密度检测样例...")
        
        # 收集花朵数量信息
        flower_data = []
        label_files = list(self.labels_dir.glob("*.txt"))
        
        for label_file in label_files:
            with open(label_file, 'r') as f:
                lines = f.readlines()
            flower_count = len([line for line in lines if line.strip()])
            
            # 查找对应的可视化文件
            image_name = label_file.stem
            viz_file = self.viz_dir / f"annotated_{image_name}.jpg"
            
            if viz_file.exists():
                flower_data.append((flower_count, image_name, viz_file))
        
        # 按花朵数量排序
        flower_data.sort(reverse=True, key=lambda x: x[0])
        top_examples = flower_data[:top_n]
        
        # 创建图表
        fig, axes = plt.subplots(1, top_n, figsize=(4*top_n, 6))
        if top_n == 1:
            axes = [axes]
        
        for i, (count, name, viz_file) in enumerate(top_examples):
            # 读取图像
            img = cv2.imread(str(viz_file))
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # 显示图像
            axes[i].imshow(img_rgb)
            axes[i].set_title(f"#{i+1}: {count} 朵花\n{name}", fontsize=10)
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.suptitle("FlowerCount-YOLO 高密度检测样例", fontsize=14, y=1.02)
        
        # 保存图表
        output_path = self.project_root / "demo_high_density_examples.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"   🏆 高密度检测排行榜:")
        for i, (count, name, _) in enumerate(top_examples):
            print(f"      #{i+1}: {name} - {count} 朵花")
        
        print(f"   ✅ 高密度样例已保存: {output_path}")
    
    def generate_project_report(self):
        """生成项目报告"""
        print("\n📋 生成项目报告...")
        
        # 收集项目信息
        report = {
            "project_name": "FlowerCount-YOLO",
            "version": "1.0.0",
            "description": "高密度花朵检测与计数系统",
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S'),
            "statistics": {},
            "file_structure": {},
            "performance": {}
        }
        
        # 统计文件信息
        image_files = list(self.data_dir.glob("*.JPG"))
        label_files = list(self.labels_dir.glob("*.txt"))
        viz_files = list(self.viz_dir.glob("*.jpg"))
        
        report["file_structure"] = {
            "original_images": len(image_files),
            "annotation_files": len(label_files),
            "visualization_files": len(viz_files)
        }
        
        # 统计花朵信息
        if label_files:
            flower_counts = []
            for label_file in label_files:
                with open(label_file, 'r') as f:
                    lines = f.readlines()
                flower_count = len([line for line in lines if line.strip()])
                flower_counts.append(flower_count)
            
            report["statistics"] = {
                "total_images": len(flower_counts),
                "total_flowers": sum(flower_counts),
                "avg_flowers_per_image": np.mean(flower_counts),
                "std_flowers_per_image": np.std(flower_counts),
                "min_flowers": min(flower_counts),
                "max_flowers": max(flower_counts),
                "median_flowers": np.median(flower_counts)
            }
        
        # 保存报告
        report_file = self.project_root / f"FlowerCount-YOLO_Demo_Report_{time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   ✅ 项目报告已保存: {report_file}")
        
        # 打印摘要
        print(f"\n📊 项目摘要:")
        print(f"   🌸 项目名称: {report['project_name']}")
        print(f"   📅 生成时间: {report['timestamp']}")
        print(f"   🖼️  图像总数: {report['file_structure']['original_images']}")
        print(f"   🏷️  标注文件: {report['file_structure']['annotation_files']}")
        print(f"   🎨 可视化文件: {report['file_structure']['visualization_files']}")
        
        if report["statistics"]:
            stats = report["statistics"]
            print(f"   🌸 花朵总数: {stats['total_flowers']}")
            print(f"   📈 平均每图: {stats['avg_flowers_per_image']:.1f} 朵")
            print(f"   📊 检测范围: {stats['min_flowers']} - {stats['max_flowers']} 朵")
    
    def run_full_demo(self):
        """运行完整演示"""
        print("\n🚀 开始FlowerCount-YOLO完整演示")
        print("=" * 60)
        
        try:
            # 1. 分析数据集统计
            self.analyze_dataset_statistics()
            
            # 2. 展示样本检测结果
            self.show_sample_detections(6)
            
            # 3. 分析花朵分布
            self.analyze_flower_distribution()
            
            # 4. 展示高密度样例
            self.show_high_density_examples(5)
            
            # 5. 生成项目报告
            self.generate_project_report()
            
            print("\n" + "=" * 60)
            print("🎉 FlowerCount-YOLO演示完成！")
            print("📁 所有结果已保存到项目目录")
            print("🌸 感谢使用FlowerCount-YOLO系统！")
            
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    demo = FlowerCountYOLODemo()
    demo.run_full_demo()

if __name__ == "__main__":
    main()
