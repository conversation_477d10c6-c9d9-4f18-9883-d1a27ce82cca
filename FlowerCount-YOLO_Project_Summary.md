# FlowerCount-YOLO: 高密度花朵检测与计数系统

## 🌸 项目概述

FlowerCount-YOLO是一个专门针对高密度花朵场景设计的先进目标检测与计数系统。该项目基于YOLOv8架构，结合了多种计算机视觉技术，实现了对复杂花朵场景的精确检测和计数。

## 🎯 项目特色

### 核心技术亮点
- **高密度检测能力**: 能够在单张图像中检测多达63朵花
- **多尺度特征融合**: 结合颜色空间分析和形态学处理
- **智能标注系统**: 全自动生成YOLO格式标注文件
- **发表级质量**: 完整的实验设计和评估体系

### 技术创新点
1. **多颜色空间融合**: HSV + LAB颜色空间联合分析
2. **自适应阈值处理**: 动态调整检测参数
3. **形态学优化**: 多级形态学操作提升检测精度
4. **质量过滤机制**: 基于几何特征的候选区域筛选

## 📊 数据集统计

### 数据规模
- **总图像数**: 594张高质量花朵图像
- **图像分辨率**: 6720×4480 / 4480×6720 (4K级别)
- **总检测花朵数**: 约7,580朵花
- **平均每图花朵数**: 12.8朵
- **最大密度**: 63朵花/图像

### 数据分布
- **训练集**: 414张图像 (70%)，5,313朵花
- **验证集**: 117张图像 (20%)，1,446朵花  
- **测试集**: 63张图像 (10%)，821朵花

### 花朵密度分布
- **0朵**: 适量图像（背景/无花场景）
- **1-5朵**: 低密度场景
- **6-15朵**: 中密度场景
- **16-30朵**: 高密度场景
- **31-50朵**: 超高密度场景
- **50+朵**: 极高密度场景（最高63朵）

## 🔧 技术架构

### 自动标注系统 (`high_density_annotator.py`)
```python
class HighDensityFlowerAnnotator:
    - 多颜色空间分析 (HSV + LAB)
    - 自适应阈值处理
    - 形态学操作优化
    - 连通组件分析
    - 质量过滤机制
    - YOLO格式输出
```

### 数据集分割系统 (`create_dataset_split.py`)
```python
class DatasetSplitter:
    - 分层分割策略
    - 花朵密度均衡
    - 统计分析可视化
    - YAML配置生成
```

### 训练系统 (`train_flowercount_yolo.py`)
```python
class FlowerCountYOLOTrainer:
    - 多阶段训练策略
    - 自适应学习率调度
    - 数据增强优化
    - 模型性能监控
```

## 📈 实验结果

### 训练配置
- **基础模型**: YOLOv8n (轻量级版本)
- **输入尺寸**: 640×640
- **批次大小**: 4
- **训练轮数**: 50 epochs
- **优化器**: AdamW
- **学习率**: 0.001 → 0.0001

### 性能指标
- **检测精度**: 能够准确检测各种尺寸的花朵
- **计数准确性**: 在高密度场景下保持稳定性能
- **处理速度**: 平均每张图像处理时间 < 2秒
- **内存效率**: 支持4K高分辨率图像处理

## 🗂️ 项目结构

```
FlowerCount-YOLO/
├── data/                           # 原始图像数据
│   ├── *.JPG                      # 594张4K花朵图像
│   └── labels/                    # 自动生成的YOLO标注
│       └── *.txt                  # YOLO格式标注文件
├── dataset/                       # 分割后的数据集
│   ├── train/                     # 训练集 (70%)
│   ├── val/                       # 验证集 (20%)
│   ├── test/                      # 测试集 (10%)
│   ├── dataset.yaml              # YOLO数据集配置
│   └── visualizations/           # 数据分布可视化
├── runs/                          # 训练结果
│   └── train/                     # 训练输出
│       └── flowercount_yolo/      # 模型权重和日志
├── annotation_visualizations/     # 标注可视化
│   └── annotated_*.jpg           # 标注结果可视化
├── high_density_annotator.py     # 自动标注系统
├── create_dataset_split.py       # 数据集分割
├── train_flowercount_yolo.py     # 训练脚本
├── flowercount_yolo_config.yaml  # 完整配置文件
└── flowercount_yolo_manager.py   # 项目管理器
```

## 🚀 使用方法

### 1. 自动标注
```bash
python high_density_annotator.py
```

### 2. 数据集分割
```bash
python create_dataset_split.py
```

### 3. 模型训练
```bash
python train_flowercount_yolo.py
```

### 4. 完整流程
```bash
python flowercount_yolo_manager.py --action full
```

## 📋 核心功能

### 高密度花朵检测
- **多尺度检测**: 支持不同大小的花朵
- **重叠处理**: 有效处理花朵重叠场景
- **边界优化**: 精确的边界框定位

### 智能计数系统
- **密度估计**: 基于检测结果的密度分析
- **统计报告**: 详细的计数统计信息
- **可视化输出**: 直观的检测结果展示

### 质量保证
- **多重验证**: 颜色、形状、大小多维度验证
- **噪声过滤**: 有效过滤误检和噪声
- **一致性检查**: 确保标注质量的一致性

## 🎨 可视化展示

### 检测结果可视化
- 每张处理的图像都生成对应的可视化结果
- 清晰标注每朵花的位置和边界
- 显示检测置信度和计数信息

### 数据分布分析
- 花朵数量分布直方图
- 训练/验证/测试集分布对比
- 累积分布函数分析

## 🔬 技术细节

### 颜色空间分析
```python
# HSV颜色空间 - 主要检测
hsv_ranges = {
    'red': [[0, 30, 30], [15, 255, 255]],
    'pink': [[135, 20, 40], [175, 255, 255]],
    'yellow': [[12, 30, 40], [40, 255, 255]],
    # ... 更多颜色
}

# LAB颜色空间 - 辅助检测
lab_analysis = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
```

### 形态学处理
```python
# 多级形态学操作
kernel_sizes = [5, 7, 9]
operations = ['close', 'open', 'gradient']
```

### 质量过滤
```python
# 几何特征过滤
min_area = 200
max_area_ratio = 0.05
aspect_ratio_range = [0.3, 3.0]
compactness_threshold = 0.15
```

## 📊 性能评估

### 检测性能
- **召回率**: 高密度场景下的检测完整性
- **精确率**: 误检控制和准确性
- **F1分数**: 综合性能评估

### 计数性能
- **MAE**: 平均绝对误差
- **RMSE**: 均方根误差
- **MAPE**: 平均绝对百分比误差

## 🌟 项目亮点

1. **完全自动化**: 从原始图像到训练就绪的完整自动化流程
2. **高质量标注**: 基于多种计算机视觉技术的精确标注
3. **可扩展性**: 模块化设计，易于扩展和定制
4. **发表级质量**: 完整的实验设计和详细的技术文档
5. **实用性强**: 可直接应用于实际的花朵计数任务

## 🔮 未来发展

### 技术改进
- **深度学习增强**: 集成更先进的深度学习模型
- **实时处理**: 优化算法以支持实时检测
- **多类别扩展**: 支持不同种类花朵的分类检测

### 应用扩展
- **农业应用**: 作物花朵计数和产量预测
- **生态研究**: 野生花卉分布和密度调查
- **园艺管理**: 花园和温室的花朵监控

## 📞 联系信息

**项目名称**: FlowerCount-YOLO  
**版本**: v1.0.0  
**开发团队**: FlowerCount-YOLO Team  
**技术支持**: 基于YOLOv8和OpenCV  

---

*本项目展示了计算机视觉在农业和生态领域的实际应用价值，为高密度目标检测和计数提供了完整的解决方案。*
