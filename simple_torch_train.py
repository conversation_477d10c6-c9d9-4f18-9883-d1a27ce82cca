#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的PyTorch训练脚本 - FlowerCount-YOLO
"""

import os
import sys
import time
import json
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim

def main():
    print("🌸 FlowerCount-YOLO 简单训练")
    print("=" * 50)
    
    # 检查环境
    print("🔍 检查环境...")
    print(f"   Python版本: {sys.version}")
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
        device = torch.device('cuda')
    else:
        print("   使用CPU")
        device = torch.device('cpu')
    
    # 检查数据集
    print("\n📊 检查数据集...")
    dataset_dir = Path("dataset")
    train_dir = dataset_dir / "train" / "images"
    val_dir = dataset_dir / "val" / "images"
    
    if not train_dir.exists() or not val_dir.exists():
        print("❌ 数据集目录不存在")
        return False
    
    train_images = list(train_dir.glob("*.jpg"))
    val_images = list(val_dir.glob("*.jpg"))
    
    print(f"   训练图像: {len(train_images)} 张")
    print(f"   验证图像: {len(val_images)} 张")
    
    # 创建简单模型
    print("\n🎯 创建简单模型...")
    
    class SimpleFlowerDetector(nn.Module):
        def __init__(self):
            super().__init__()
            self.conv1 = nn.Conv2d(3, 16, 3, padding=1)
            self.conv2 = nn.Conv2d(16, 32, 3, padding=1)
            self.conv3 = nn.Conv2d(32, 64, 3, padding=1)
            self.pool = nn.AdaptiveAvgPool2d((1, 1))
            self.fc = nn.Linear(64, 1)  # 预测花朵数量
            self.relu = nn.ReLU()
        
        def forward(self, x):
            x = self.relu(self.conv1(x))
            x = self.relu(self.conv2(x))
            x = self.relu(self.conv3(x))
            x = self.pool(x)
            x = x.view(x.size(0), -1)
            x = self.fc(x)
            return x
    
    model = SimpleFlowerDetector().to(device)
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.MSELoss()
    
    print("✅ 模型创建成功")
    
    # 模拟训练数据
    print("\n🚀 开始模拟训练...")
    
    num_epochs = 10
    batch_size = 4
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        model.train()
        total_loss = 0.0
        
        # 模拟训练批次
        for batch in range(10):  # 模拟10个批次
            # 创建随机输入数据 (模拟图像)
            inputs = torch.randn(batch_size, 3, 224, 224).to(device)
            targets = torch.randn(batch_size, 1).to(device)  # 模拟花朵数量
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        avg_loss = total_loss / 10
        print(f"   Epoch {epoch+1}/{num_epochs}, Loss: {avg_loss:.4f}")
    
    training_time = time.time() - start_time
    
    # 保存模型
    print("\n💾 保存模型...")
    torch.save(model.state_dict(), 'simple_flower_model.pth')
    
    # 保存训练信息
    train_info = {
        'framework': f'PyTorch {torch.__version__}',
        'device': str(device),
        'cuda_available': torch.cuda.is_available(),
        'epochs': num_epochs,
        'batch_size': batch_size,
        'training_time_minutes': training_time / 60,
        'model_file': 'simple_flower_model.pth',
        'dataset_info': {
            'train_images': len(train_images),
            'val_images': len(val_images)
        },
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    with open('simple_training_info.json', 'w', encoding='utf-8') as f:
        json.dump(train_info, f, indent=2, ensure_ascii=False)
    
    print(f"🎉 训练完成！")
    print(f"   训练时间: {training_time:.1f} 秒")
    print(f"   模型文件: simple_flower_model.pth")
    print(f"   训练信息: simple_training_info.json")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 简单训练完成成功！")
        print("这是一个基础的PyTorch训练演示")
    else:
        print("\n❌ 训练失败！")
