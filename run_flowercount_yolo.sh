#!/bin/bash

echo ""
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                        FlowerCount-YOLO Auto Setup                          ║"
echo "║                                                                              ║"
echo "║    🌸 Advanced Flower Detection and Counting System                         ║"
echo "║    🔧 Automated Environment Setup and Project Launch                        ║"
echo "║                                                                              ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo ""

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Conda not found!"
    echo "Please install Anaconda or Miniconda first:"
    echo "   - Anaconda: https://www.anaconda.com/products/distribution"
    echo "   - Miniconda: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

echo "✅ Conda found!"

# Initialize conda for bash
source $(conda info --base)/etc/profile.d/conda.sh

# Check if FlowerCount-YOLO environment exists
if ! conda env list | grep -q "flowercount-yolo"; then
    echo ""
    echo "🔨 FlowerCount-YOLO environment not found. Creating it..."
    echo ""
    
    # Create environment
    echo "📦 Creating conda environment with Python 3.9..."
    conda create -n flowercount-yolo python=3.9 -y
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create environment!"
        exit 1
    fi
    
    echo "✅ Environment created successfully!"
    
    # Activate environment and install packages
    echo ""
    echo "🔥 Installing PyTorch and dependencies..."
    conda activate flowercount-yolo
    
    # Install PyTorch (CPU version for compatibility)
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install PyTorch!"
        exit 1
    fi
    
    # Install other dependencies
    pip install ultralytics>=8.0.0
    pip install opencv-python>=4.8.0
    pip install matplotlib>=3.7.0
    pip install seaborn>=0.12.0
    pip install pandas>=2.0.0
    pip install numpy>=1.24.0
    pip install scipy>=1.11.0
    pip install scikit-learn>=1.3.0
    pip install tqdm>=4.65.0
    pip install pyyaml>=6.0
    pip install pillow>=10.0.0
    
    echo "✅ Dependencies installed!"
    
else
    echo "✅ FlowerCount-YOLO environment found!"
    conda activate flowercount-yolo
fi

echo ""
echo "🚀 Starting FlowerCount-YOLO Project..."
echo ""

# Run the project starter
python start_project.py

echo ""
echo "🌸 Thank you for using FlowerCount-YOLO!"
