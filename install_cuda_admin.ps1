# FlowerCount-<PERSON>OL<PERSON> CUDA Installation Script (PowerShell)
# Run this script as Administrator

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                   FlowerCount-YOLO CUDA Installation                        ║" -ForegroundColor Cyan
Write-Host "║                        (Administrator Mode)                                 ║" -ForegroundColor Cyan
Write-Host "║                                                                              ║" -ForegroundColor Cyan
Write-Host "║    🌸 Advanced Flower Detection and Counting System                         ║" -ForegroundColor Cyan
Write-Host "║    🔥 Installing PyTorch CUDA Support                                       ║" -ForegroundColor Cyan
Write-Host "║                                                                              ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click on PowerShell and select 'Run as administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Running with Administrator privileges" -ForegroundColor Green

# Check conda installation
Write-Host ""
Write-Host "🔍 Checking conda installation..." -ForegroundColor Yellow
try {
    $condaVersion = conda --version
    Write-Host "✅ Conda found: $condaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Conda not found!" -ForegroundColor Red
    Write-Host "Please install Anaconda or Miniconda first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Show available environments
Write-Host ""
Write-Host "📋 Available conda environments:" -ForegroundColor Yellow
conda env list

# Activate or create environment
Write-Host ""
Write-Host "🎯 Setting up flowercount-yolo-cuda environment..." -ForegroundColor Yellow

try {
    conda activate flowercount-yolo-cuda
    Write-Host "✅ Environment activated" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Environment not found, creating new one..." -ForegroundColor Yellow
    conda create -n flowercount-yolo-cuda python=3.9 -y
    conda activate flowercount-yolo-cuda
    Write-Host "✅ New environment created and activated" -ForegroundColor Green
}

# Install PyTorch with CUDA
Write-Host ""
Write-Host "🔥 Installing PyTorch with CUDA 11.8 support..." -ForegroundColor Yellow
Write-Host "This may take several minutes..." -ForegroundColor Cyan

# Method 1: Try conda install
Write-Host "📦 Trying conda installation..." -ForegroundColor Yellow
try {
    conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
    Write-Host "✅ PyTorch installed successfully via conda!" -ForegroundColor Green
    $installSuccess = $true
} catch {
    Write-Host "⚠️  Conda installation failed, trying pip..." -ForegroundColor Yellow
    
    # Method 2: Try pip install
    try {
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
        Write-Host "✅ PyTorch installed successfully via pip!" -ForegroundColor Green
        $installSuccess = $true
    } catch {
        Write-Host "❌ Both installation methods failed!" -ForegroundColor Red
        $installSuccess = $false
    }
}

if (-not $installSuccess) {
    Write-Host ""
    Write-Host "❌ Installation failed!" -ForegroundColor Red
    Write-Host "🔧 Please try manual installation:" -ForegroundColor Yellow
    Write-Host "   conda activate flowercount-yolo-cuda" -ForegroundColor White
    Write-Host "   conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

# Verify PyTorch installation
Write-Host ""
Write-Host "🧪 Verifying PyTorch CUDA installation..." -ForegroundColor Yellow

$verifyScript = @"
import torch
print('PyTorch version:', torch.__version__)
print('CUDA available:', torch.cuda.is_available())
print('CUDA version:', torch.version.cuda if torch.cuda.is_available() else 'None')
print('GPU count:', torch.cuda.device_count() if torch.cuda.is_available() else 0)
if torch.cuda.is_available():
    print('GPU name:', torch.cuda.get_device_name(0))
"@

try {
    python -c $verifyScript
    Write-Host "✅ PyTorch verification successful!" -ForegroundColor Green
} catch {
    Write-Host "❌ PyTorch verification failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Install additional dependencies
Write-Host ""
Write-Host "📦 Installing additional dependencies..." -ForegroundColor Yellow

$packages = @(
    "ultralytics>=8.0.0",
    "opencv-python>=4.8.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "scikit-learn>=1.3.0",
    "tqdm>=4.65.0",
    "pyyaml>=6.0",
    "pillow>=10.0.0"
)

foreach ($package in $packages) {
    try {
        pip install $package
        Write-Host "✅ Installed: $package" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Failed to install: $package" -ForegroundColor Yellow
    }
}

# Install optional packages
Write-Host ""
Write-Host "🎯 Installing optional advanced packages..." -ForegroundColor Yellow

$optionalPackages = @("wandb", "tensorboard", "grad-cam", "transformers")

foreach ($package in $optionalPackages) {
    try {
        pip install $package
        Write-Host "✅ Installed: $package" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Failed to install: $package" -ForegroundColor Yellow
    }
}

# Final verification
Write-Host ""
Write-Host "🧪 Final verification..." -ForegroundColor Yellow

$finalVerifyScript = @"
import torch
import torchvision
import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import sklearn
import yaml
from PIL import Image
import tqdm

print('✅ All core packages imported successfully!')
print('🔥 PyTorch CUDA:', torch.cuda.is_available())
print('🎯 Ready for FlowerCount-YOLO!')
"@

try {
    python -c $finalVerifyScript
    Write-Host ""
    Write-Host "🎉 Installation completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next steps:" -ForegroundColor Cyan
    Write-Host "   1. Keep this terminal open" -ForegroundColor White
    Write-Host "   2. Run: python start_project.py" -ForegroundColor White
    Write-Host "   3. Or run: python quick_start.py --mode demo" -ForegroundColor White
    Write-Host ""
    Write-Host "🌸 FlowerCount-YOLO CUDA environment is ready!" -ForegroundColor Green
    Write-Host "🚀 You can now run the project with GPU acceleration!" -ForegroundColor Green
} catch {
    Write-Host "❌ Final verification failed!" -ForegroundColor Red
    Write-Host "Some packages may not be properly installed." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to continue"
