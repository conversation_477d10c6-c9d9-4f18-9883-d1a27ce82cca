#!/usr/bin/env python3
"""
配置pip镜像源以加速下载

这个脚本会自动配置最快的国内镜像源
"""

import os
import platform
import subprocess
import time
import requests
from pathlib import Path


def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                        pip镜像源配置工具                                    ║
║                                                                              ║
║    🚀 自动配置最快的国内镜像源                                               ║
║    📦 大幅提升pip安装速度                                                    ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def test_mirror_speed(mirror_url, timeout=5):
    """测试镜像源速度"""
    try:
        start_time = time.time()
        response = requests.get(mirror_url, timeout=timeout)
        end_time = time.time()
        
        if response.status_code == 200:
            return end_time - start_time
        else:
            return float('inf')
    except:
        return float('inf')


def find_fastest_mirror():
    """找到最快的镜像源"""
    
    mirrors = {
        "清华大学": "https://pypi.tuna.tsinghua.edu.cn/simple/",
        "阿里云": "https://mirrors.aliyun.com/pypi/simple/",
        "豆瓣": "https://pypi.douban.com/simple/",
        "中科大": "https://pypi.mirrors.ustc.edu.cn/simple/",
        "华为云": "https://mirrors.huaweicloud.com/repository/pypi/simple/",
        "腾讯云": "https://mirrors.cloud.tencent.com/pypi/simple/"
    }
    
    print("🔍 测试镜像源速度...")
    
    fastest_mirror = None
    fastest_time = float('inf')
    
    for name, url in mirrors.items():
        print(f"   测试 {name}...", end=" ")
        speed = test_mirror_speed(url)
        
        if speed < fastest_time:
            fastest_time = speed
            fastest_mirror = (name, url)
        
        if speed == float('inf'):
            print("❌ 超时")
        else:
            print(f"✅ {speed:.2f}秒")
    
    return fastest_mirror


def configure_pip_mirrors():
    """配置pip镜像源"""
    
    print("\n🚀 配置pip镜像源...")
    
    # 找到最快的镜像
    fastest = find_fastest_mirror()
    
    if fastest:
        name, url = fastest
        print(f"\n🏆 最快镜像: {name}")
        print(f"🔗 URL: {url}")
        
        # 配置pip
        try:
            subprocess.run(['pip', 'config', 'set', 'global.index-url', url], check=True)
            subprocess.run(['pip', 'config', 'set', 'global.trusted-host', url.split('/')[2]], check=True)
            print("✅ pip镜像源配置成功!")
            
            # 显示配置
            result = subprocess.run(['pip', 'config', 'list'], capture_output=True, text=True)
            print("\n📋 当前pip配置:")
            print(result.stdout)
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 配置失败: {e}")
            return False
    else:
        print("❌ 无法找到可用的镜像源")
        return False
    
    return True


def configure_conda_mirrors():
    """配置conda镜像源"""
    
    print("\n🐍 配置conda镜像源...")
    
    conda_config = """
channels:
  - defaults
show_channel_urls: true
default_channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/msys2
custom_channels:
  conda-forge: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud
  pytorch: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud
"""
    
    try:
        # 获取conda配置目录
        if platform.system() == "Windows":
            conda_dir = Path.home() / ".condarc"
        else:
            conda_dir = Path.home() / ".condarc"
        
        # 写入配置
        with open(conda_dir, 'w', encoding='utf-8') as f:
            f.write(conda_config)
        
        print("✅ conda镜像源配置成功!")
        
        # 清理conda缓存
        subprocess.run(['conda', 'clean', '-i'], check=True)
        print("✅ conda缓存已清理")
        
    except Exception as e:
        print(f"⚠️  conda配置失败: {e}")


def create_fast_install_commands():
    """创建快速安装命令"""
    
    print("\n📝 生成快速安装命令...")
    
    # PyTorch安装命令
    pytorch_commands = [
        "# PyTorch CUDA安装（多个镜像源备选）",
        "pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/",
        "# 如果上面失败，尝试:",
        "# pip install torch torchvision torchaudio -i https://mirrors.aliyun.com/pypi/simple/",
        "# pip install torch torchvision torchaudio -i https://pypi.douban.com/simple/",
        ""
    ]
    
    # 依赖包安装命令
    deps_commands = [
        "# 核心依赖包（批量安装）",
        "pip install ultralytics opencv-python opencv-contrib-python matplotlib seaborn pandas numpy scipy scikit-learn tqdm pyyaml pillow albumentations scikit-image wandb tensorboard mlflow grad-cam transformers timm pycocotools torchmetrics rich hydra-core",
        ""
    ]
    
    # 保存到文件
    with open("fast_install_commands.txt", 'w', encoding='utf-8') as f:
        f.write("# FlowerCount-YOLO 快速安装命令\n")
        f.write("# 使用国内镜像源加速下载\n\n")
        f.write("# 1. 创建环境\n")
        f.write("conda create -n flowercount-yolo-cuda python=3.9 -y\n")
        f.write("conda activate flowercount-yolo-cuda\n\n")
        f.write("# 2. 配置镜像源\n")
        f.write("pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/\n")
        f.write("pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn\n\n")
        f.write("# 3. 安装PyTorch\n")
        f.writelines([cmd + "\n" for cmd in pytorch_commands])
        f.write("# 4. 安装依赖\n")
        f.writelines([cmd + "\n" for cmd in deps_commands])
        f.write("# 5. 验证安装\n")
        f.write("python -c \"import torch; print('CUDA:', torch.cuda.is_available())\"\n")
    
    print("✅ 快速安装命令已保存到: fast_install_commands.txt")


def main():
    """主函数"""
    
    print_banner()
    
    # 配置pip镜像源
    if configure_pip_mirrors():
        print("\n🎉 pip镜像源配置完成!")
    
    # 配置conda镜像源
    configure_conda_mirrors()
    
    # 生成快速安装命令
    create_fast_install_commands()
    
    print("\n📋 配置完成! 现在可以享受飞速的pip安装了!")
    print("\n🚀 下一步:")
    print("   1. 运行: install_fast.bat")
    print("   2. 或查看: fast_install_commands.txt")
    print("   3. 手动执行快速安装命令")
    
    print("\n🌸 Happy coding with FlowerCount-YOLO!")


if __name__ == "__main__":
    main()
