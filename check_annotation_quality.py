#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 标注质量检查工具
检查自动标注的准确性和质量
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import random
from typing import List, Tuple, Dict

class AnnotationQualityChecker:
    """标注质量检查器"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.images_dir = self.data_dir
        self.labels_dir = self.data_dir / "labels"
        self.output_dir = Path("annotation_quality_check")
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"🔍 标注质量检查器初始化")
        print(f"   图像目录: {self.images_dir}")
        print(f"   标签目录: {self.labels_dir}")
        print(f"   输出目录: {self.output_dir}")
    
    def load_annotations(self, label_file: Path) -> List[Tuple[float, float, float, float]]:
        """加载YOLO格式的标注"""
        annotations = []
        if label_file.exists():
            with open(label_file, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        # YOLO格式: class x_center y_center width height
                        cls, x, y, w, h = map(float, parts[:5])
                        annotations.append((x, y, w, h))
        return annotations
    
    def yolo_to_bbox(self, x_center: float, y_center: float, width: float, height: float, 
                     img_width: int, img_height: int) -> Tuple[int, int, int, int]:
        """将YOLO格式转换为边界框坐标"""
        x_center *= img_width
        y_center *= img_height
        width *= img_width
        height *= img_height
        
        x1 = int(x_center - width / 2)
        y1 = int(y_center - height / 2)
        x2 = int(x_center + width / 2)
        y2 = int(y_center + height / 2)
        
        return x1, y1, x2, y2
    
    def visualize_annotations(self, image_path: Path, annotations: List[Tuple], 
                            save_path: Path) -> Dict:
        """可视化标注结果"""
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            return {"error": "无法读取图像"}
        
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        img_height, img_width = image.shape[:2]
        
        # 创建副本用于绘制
        vis_image = image.copy()
        
        # 绘制每个标注框
        for i, (x, y, w, h) in enumerate(annotations):
            x1, y1, x2, y2 = self.yolo_to_bbox(x, y, w, h, img_width, img_height)
            
            # 绘制边界框
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 添加编号
            cv2.putText(vis_image, f'{i+1}', (x1, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # 保存可视化结果
        plt.figure(figsize=(15, 10))
        plt.imshow(vis_image)
        plt.title(f'标注检查: {image_path.name}\n检测到 {len(annotations)} 朵花', 
                 fontsize=14, fontweight='bold')
        plt.axis('off')
        
        # 添加统计信息
        stats_text = f"图像尺寸: {img_width}×{img_height}\n"
        stats_text += f"检测数量: {len(annotations)}\n"
        if annotations:
            areas = [(w * img_width) * (h * img_height) for _, _, w, h in annotations]
            stats_text += f"平均面积: {np.mean(areas):.0f}px²\n"
            stats_text += f"面积范围: {min(areas):.0f}-{max(areas):.0f}px²"
        
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
                fontsize=10, verticalalignment='top', 
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        return {
            "image_size": (img_width, img_height),
            "flower_count": len(annotations),
            "areas": [(w * img_width) * (h * img_height) for _, _, w, h in annotations] if annotations else [],
            "visualization_saved": str(save_path)
        }
    
    def analyze_annotation_statistics(self) -> Dict:
        """分析标注统计信息"""
        print("\n📊 分析标注统计信息...")
        
        image_files = list(self.images_dir.glob("*.JPG")) + list(self.images_dir.glob("*.jpg"))
        total_images = len(image_files)
        total_flowers = 0
        flower_counts = []
        areas_all = []
        
        annotated_count = 0
        
        for img_file in image_files:
            label_file = self.labels_dir / f"{img_file.stem}.txt"
            annotations = self.load_annotations(label_file)
            
            if annotations:
                annotated_count += 1
                flower_count = len(annotations)
                total_flowers += flower_count
                flower_counts.append(flower_count)
                
                # 计算面积（需要图像尺寸）
                try:
                    image = cv2.imread(str(img_file))
                    if image is not None:
                        img_height, img_width = image.shape[:2]
                        areas = [(w * img_width) * (h * img_height) for _, _, w, h in annotations]
                        areas_all.extend(areas)
                except:
                    pass
        
        stats = {
            "total_images": total_images,
            "annotated_images": annotated_count,
            "annotation_rate": annotated_count / total_images if total_images > 0 else 0,
            "total_flowers": total_flowers,
            "avg_flowers_per_image": total_flowers / annotated_count if annotated_count > 0 else 0,
            "flower_count_distribution": {
                "min": min(flower_counts) if flower_counts else 0,
                "max": max(flower_counts) if flower_counts else 0,
                "mean": np.mean(flower_counts) if flower_counts else 0,
                "std": np.std(flower_counts) if flower_counts else 0
            },
            "area_statistics": {
                "min": min(areas_all) if areas_all else 0,
                "max": max(areas_all) if areas_all else 0,
                "mean": np.mean(areas_all) if areas_all else 0,
                "std": np.std(areas_all) if areas_all else 0
            } if areas_all else {}
        }
        
        return stats
    
    def check_random_samples(self, num_samples: int = 10) -> List[Dict]:
        """检查随机样本"""
        print(f"\n🎲 检查 {num_samples} 个随机样本...")
        
        image_files = list(self.images_dir.glob("*.JPG")) + list(self.images_dir.glob("*.jpg"))
        
        if len(image_files) == 0:
            print("❌ 没有找到图像文件")
            return []
        
        # 随机选择样本
        sample_files = random.sample(image_files, min(num_samples, len(image_files)))
        
        results = []
        
        for i, img_file in enumerate(sample_files):
            print(f"   检查样本 {i+1}/{len(sample_files)}: {img_file.name}")
            
            label_file = self.labels_dir / f"{img_file.stem}.txt"
            annotations = self.load_annotations(label_file)
            
            # 生成可视化
            vis_path = self.output_dir / f"sample_{i+1:02d}_{img_file.stem}.jpg"
            result = self.visualize_annotations(img_file, annotations, vis_path)
            result["image_name"] = img_file.name
            result["sample_index"] = i + 1
            
            results.append(result)
        
        return results
    
    def generate_quality_report(self) -> Dict:
        """生成质量报告"""
        print("\n📋 生成质量报告...")
        
        # 统计分析
        stats = self.analyze_annotation_statistics()
        
        # 随机样本检查
        samples = self.check_random_samples(15)
        
        # 生成报告
        report = {
            "timestamp": str(pd.Timestamp.now()) if 'pd' in globals() else "2025-08-19",
            "statistics": stats,
            "sample_checks": samples,
            "quality_assessment": self.assess_quality(stats, samples)
        }
        
        # 保存报告
        report_path = self.output_dir / "annotation_quality_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report
    
    def assess_quality(self, stats: Dict, samples: List[Dict]) -> Dict:
        """评估标注质量"""
        assessment = {
            "overall_score": 0,
            "coverage_score": 0,
            "consistency_score": 0,
            "density_score": 0,
            "recommendations": []
        }
        
        # 覆盖率评分
        coverage_rate = stats.get("annotation_rate", 0)
        if coverage_rate > 0.9:
            assessment["coverage_score"] = 100
        elif coverage_rate > 0.7:
            assessment["coverage_score"] = 80
        elif coverage_rate > 0.5:
            assessment["coverage_score"] = 60
        else:
            assessment["coverage_score"] = 40
            assessment["recommendations"].append("标注覆盖率较低，建议检查标注流程")
        
        # 一致性评分（基于花朵数量的标准差）
        flower_std = stats.get("flower_count_distribution", {}).get("std", 0)
        if flower_std < 5:
            assessment["consistency_score"] = 100
        elif flower_std < 10:
            assessment["consistency_score"] = 80
        elif flower_std < 15:
            assessment["consistency_score"] = 60
        else:
            assessment["consistency_score"] = 40
            assessment["recommendations"].append("花朵数量变化较大，建议检查标注一致性")
        
        # 密度评分
        avg_flowers = stats.get("avg_flowers_per_image", 0)
        if avg_flowers > 10:
            assessment["density_score"] = 100
        elif avg_flowers > 5:
            assessment["density_score"] = 80
        elif avg_flowers > 2:
            assessment["density_score"] = 60
        else:
            assessment["density_score"] = 40
            assessment["recommendations"].append("平均花朵密度较低，建议检查检测参数")
        
        # 总体评分
        assessment["overall_score"] = (
            assessment["coverage_score"] * 0.4 +
            assessment["consistency_score"] * 0.3 +
            assessment["density_score"] * 0.3
        )
        
        return assessment

def main():
    """主函数"""
    print("🌸 FlowerCount-YOLO 标注质量检查")
    print("=" * 60)
    
    # 创建检查器
    checker = AnnotationQualityChecker()
    
    # 生成质量报告
    report = checker.generate_quality_report()
    
    # 打印结果
    print("\n📊 标注质量报告")
    print("=" * 40)
    
    stats = report["statistics"]
    print(f"📁 总图像数: {stats['total_images']}")
    print(f"🏷️  已标注图像: {stats['annotated_images']}")
    print(f"📈 标注覆盖率: {stats['annotation_rate']:.1%}")
    print(f"🌸 总花朵数: {stats['total_flowers']}")
    print(f"📊 平均每图花朵数: {stats['avg_flowers_per_image']:.1f}")
    
    flower_dist = stats["flower_count_distribution"]
    print(f"📊 花朵数量分布: {flower_dist['min']:.0f}-{flower_dist['max']:.0f} (平均: {flower_dist['mean']:.1f})")
    
    # 质量评估
    assessment = report["quality_assessment"]
    print(f"\n🎯 质量评估")
    print(f"   总体评分: {assessment['overall_score']:.0f}/100")
    print(f"   覆盖率评分: {assessment['coverage_score']:.0f}/100")
    print(f"   一致性评分: {assessment['consistency_score']:.0f}/100")
    print(f"   密度评分: {assessment['density_score']:.0f}/100")
    
    if assessment["recommendations"]:
        print(f"\n💡 建议:")
        for rec in assessment["recommendations"]:
            print(f"   • {rec}")
    
    print(f"\n📁 检查结果保存在: annotation_quality_check/")
    print(f"   • 质量报告: annotation_quality_report.json")
    print(f"   • 样本可视化: sample_*.jpg")
    
    print("\n✅ 标注质量检查完成！")

if __name__ == "__main__":
    main()
