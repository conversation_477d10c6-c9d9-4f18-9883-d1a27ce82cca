#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 数据集分割脚本
创建训练/验证/测试集分割，确保数据分布均衡
"""

import os
import shutil
import random
import json
from pathlib import Path
from typing import List, Dict, Tuple
import numpy as np
import cv2
import matplotlib.pyplot as plt

class DatasetSplitter:
    """数据集分割器"""
    
    def __init__(self, 
                 data_dir: str = "data",
                 output_dir: str = "dataset",
                 train_ratio: float = 0.7,
                 val_ratio: float = 0.2,
                 test_ratio: float = 0.1,
                 seed: int = 42):
        """
        初始化数据集分割器
        
        Args:
            data_dir: 原始数据目录
            output_dir: 输出目录
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            test_ratio: 测试集比例
            seed: 随机种子
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        self.seed = seed
        
        # 设置随机种子
        random.seed(seed)
        np.random.seed(seed)
        
        # 验证比例
        total_ratio = train_ratio + val_ratio + test_ratio
        if abs(total_ratio - 1.0) > 1e-6:
            raise ValueError(f"比例总和必须为1.0，当前为{total_ratio}")
        
        print("📊 DatasetSplitter 初始化完成")
        print(f"   📁 数据目录: {self.data_dir}")
        print(f"   📁 输出目录: {self.output_dir}")
        print(f"   📈 分割比例: 训练{train_ratio:.1%} | 验证{val_ratio:.1%} | 测试{test_ratio:.1%}")
    
    def analyze_dataset(self) -> Dict:
        """分析数据集统计信息"""
        print("\n🔍 分析数据集...")
        
        # 查找图像和标注文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.JPG', '.JPEG']
        image_files = []
        for ext in image_extensions:
            image_files.extend(list(self.data_dir.glob(f"*{ext}")))
        
        labels_dir = self.data_dir / "labels"
        label_files = list(labels_dir.glob("*.txt")) if labels_dir.exists() else []
        
        # 统计花朵数量分布
        flower_counts = []
        valid_pairs = []
        
        for img_file in image_files:
            label_file = labels_dir / f"{img_file.stem}.txt"
            if label_file.exists():
                # 读取标注文件
                with open(label_file, 'r') as f:
                    lines = f.readlines()
                
                flower_count = len([line for line in lines if line.strip()])
                flower_counts.append(flower_count)
                valid_pairs.append((img_file, label_file, flower_count))
        
        # 计算统计信息
        stats = {
            'total_images': len(image_files),
            'total_labels': len(label_files),
            'valid_pairs': len(valid_pairs),
            'flower_counts': flower_counts,
            'total_flowers': sum(flower_counts),
            'avg_flowers_per_image': np.mean(flower_counts) if flower_counts else 0,
            'std_flowers_per_image': np.std(flower_counts) if flower_counts else 0,
            'min_flowers': min(flower_counts) if flower_counts else 0,
            'max_flowers': max(flower_counts) if flower_counts else 0,
            'median_flowers': np.median(flower_counts) if flower_counts else 0
        }
        
        print(f"   📊 总图像数: {stats['total_images']}")
        print(f"   🏷️  总标注数: {stats['total_labels']}")
        print(f"   ✅ 有效配对: {stats['valid_pairs']}")
        print(f"   🌸 总花朵数: {stats['total_flowers']}")
        print(f"   📈 平均每图: {stats['avg_flowers_per_image']:.1f} ± {stats['std_flowers_per_image']:.1f}")
        print(f"   📊 花朵范围: {stats['min_flowers']} - {stats['max_flowers']} (中位数: {stats['median_flowers']:.1f})")
        
        return stats, valid_pairs
    
    def stratified_split(self, valid_pairs: List[Tuple]) -> Dict[str, List]:
        """基于花朵数量的分层分割"""
        print("\n🎯 执行分层分割...")
        
        # 按花朵数量分组
        flower_groups = {}
        for img_file, label_file, flower_count in valid_pairs:
            # 创建花朵数量区间
            if flower_count == 0:
                group = "0"
            elif flower_count <= 5:
                group = "1-5"
            elif flower_count <= 15:
                group = "6-15"
            elif flower_count <= 30:
                group = "16-30"
            elif flower_count <= 50:
                group = "31-50"
            else:
                group = "50+"
            
            if group not in flower_groups:
                flower_groups[group] = []
            flower_groups[group].append((img_file, label_file, flower_count))
        
        print("   📊 花朵数量分组:")
        for group, items in flower_groups.items():
            print(f"      {group} 朵: {len(items)} 张图像")
        
        # 对每组进行分割
        train_set = []
        val_set = []
        test_set = []
        
        for group, items in flower_groups.items():
            # 随机打乱
            random.shuffle(items)
            
            # 计算分割点
            n_items = len(items)
            n_train = int(n_items * self.train_ratio)
            n_val = int(n_items * self.val_ratio)
            
            # 分割
            train_items = items[:n_train]
            val_items = items[n_train:n_train + n_val]
            test_items = items[n_train + n_val:]
            
            train_set.extend(train_items)
            val_set.extend(val_items)
            test_set.extend(test_items)
            
            print(f"      {group} 朵分割: 训练{len(train_items)} | 验证{len(val_items)} | 测试{len(test_items)}")
        
        # 最终打乱
        random.shuffle(train_set)
        random.shuffle(val_set)
        random.shuffle(test_set)
        
        split_data = {
            'train': train_set,
            'val': val_set,
            'test': test_set
        }
        
        print(f"\n   ✅ 分割完成:")
        print(f"      训练集: {len(train_set)} 张图像")
        print(f"      验证集: {len(val_set)} 张图像")
        print(f"      测试集: {len(test_set)} 张图像")
        
        return split_data
    
    def create_directory_structure(self):
        """创建目录结构"""
        print("\n📁 创建目录结构...")
        
        # 创建主目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        for split in ['train', 'val', 'test']:
            (self.output_dir / split / 'images').mkdir(parents=True, exist_ok=True)
            (self.output_dir / split / 'labels').mkdir(parents=True, exist_ok=True)
        
        print(f"   ✅ 目录结构创建完成: {self.output_dir}")
    
    def copy_files(self, split_data: Dict[str, List]):
        """复制文件到对应目录"""
        print("\n📋 复制文件...")
        
        for split_name, items in split_data.items():
            print(f"   📂 处理 {split_name} 集...")
            
            images_dir = self.output_dir / split_name / 'images'
            labels_dir = self.output_dir / split_name / 'labels'
            
            for i, (img_file, label_file, flower_count) in enumerate(items):
                # 复制图像
                dst_img = images_dir / img_file.name
                shutil.copy2(img_file, dst_img)
                
                # 复制标注
                dst_label = labels_dir / label_file.name
                shutil.copy2(label_file, dst_label)
                
                if (i + 1) % 50 == 0:
                    print(f"      已处理 {i + 1}/{len(items)} 个文件")
            
            print(f"   ✅ {split_name} 集完成: {len(items)} 个文件")
    
    def create_yaml_config(self, split_data: Dict[str, List]):
        """创建YOLO数据集配置文件"""
        print("\n📝 创建YOLO配置文件...")
        
        # 计算统计信息
        train_flowers = sum(item[2] for item in split_data['train'])
        val_flowers = sum(item[2] for item in split_data['val'])
        test_flowers = sum(item[2] for item in split_data['test'])
        
        # 创建配置
        config = {
            'path': str(self.output_dir.absolute()),
            'train': 'train/images',
            'val': 'val/images',
            'test': 'test/images',
            'nc': 1,
            'names': ['flower'],
            'dataset_info': {
                'total_images': len(split_data['train']) + len(split_data['val']) + len(split_data['test']),
                'train_images': len(split_data['train']),
                'val_images': len(split_data['val']),
                'test_images': len(split_data['test']),
                'total_flowers': train_flowers + val_flowers + test_flowers,
                'train_flowers': train_flowers,
                'val_flowers': val_flowers,
                'test_flowers': test_flowers,
                'avg_flowers_per_image': {
                    'train': train_flowers / len(split_data['train']) if split_data['train'] else 0,
                    'val': val_flowers / len(split_data['val']) if split_data['val'] else 0,
                    'test': test_flowers / len(split_data['test']) if split_data['test'] else 0
                }
            }
        }
        
        # 保存YAML文件
        import yaml
        yaml_file = self.output_dir / 'dataset.yaml'
        with open(yaml_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        # 保存JSON文件（备份）
        json_file = self.output_dir / 'dataset_config.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ 配置文件保存:")
        print(f"      YAML: {yaml_file}")
        print(f"      JSON: {json_file}")
        
        return config
    
    def visualize_distribution(self, split_data: Dict[str, List], stats: Dict):
        """可视化数据分布"""
        print("\n📊 生成分布可视化...")
        
        # 创建可视化目录
        viz_dir = self.output_dir / 'visualizations'
        viz_dir.mkdir(exist_ok=True)
        
        # 1. 花朵数量分布
        plt.figure(figsize=(15, 10))
        
        # 子图1: 整体分布
        plt.subplot(2, 3, 1)
        plt.hist(stats['flower_counts'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('整体花朵数量分布')
        plt.xlabel('花朵数量')
        plt.ylabel('图像数量')
        plt.grid(True, alpha=0.3)
        
        # 子图2: 各集合分布
        plt.subplot(2, 3, 2)
        train_counts = [item[2] for item in split_data['train']]
        val_counts = [item[2] for item in split_data['val']]
        test_counts = [item[2] for item in split_data['test']]
        
        plt.hist([train_counts, val_counts, test_counts], 
                bins=15, alpha=0.7, 
                label=['训练集', '验证集', '测试集'],
                color=['blue', 'orange', 'green'])
        plt.title('各集合花朵数量分布')
        plt.xlabel('花朵数量')
        plt.ylabel('图像数量')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 子图3: 集合大小对比
        plt.subplot(2, 3, 3)
        sizes = [len(split_data['train']), len(split_data['val']), len(split_data['test'])]
        labels = ['训练集', '验证集', '测试集']
        colors = ['blue', 'orange', 'green']
        plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('数据集分割比例')
        
        # 子图4: 花朵总数对比
        plt.subplot(2, 3, 4)
        flower_totals = [sum(train_counts), sum(val_counts), sum(test_counts)]
        plt.bar(labels, flower_totals, color=colors, alpha=0.7)
        plt.title('各集合花朵总数')
        plt.ylabel('花朵数量')
        plt.grid(True, alpha=0.3)
        
        # 子图5: 平均花朵数对比
        plt.subplot(2, 3, 5)
        avg_flowers = [
            np.mean(train_counts) if train_counts else 0,
            np.mean(val_counts) if val_counts else 0,
            np.mean(test_counts) if test_counts else 0
        ]
        plt.bar(labels, avg_flowers, color=colors, alpha=0.7)
        plt.title('各集合平均花朵数')
        plt.ylabel('平均花朵数')
        plt.grid(True, alpha=0.3)
        
        # 子图6: 累积分布
        plt.subplot(2, 3, 6)
        sorted_counts = sorted(stats['flower_counts'])
        cumulative = np.arange(1, len(sorted_counts) + 1) / len(sorted_counts)
        plt.plot(sorted_counts, cumulative, 'b-', linewidth=2)
        plt.title('花朵数量累积分布')
        plt.xlabel('花朵数量')
        plt.ylabel('累积概率')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = viz_dir / 'dataset_distribution.png'
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ 分布图表保存: {chart_file}")
        
        return chart_file
    
    def run_split(self):
        """运行完整的数据集分割流程"""
        print("🚀 开始数据集分割流程")
        print("=" * 50)
        
        try:
            # 1. 分析数据集
            stats, valid_pairs = self.analyze_dataset()
            
            if not valid_pairs:
                print("❌ 未找到有效的图像-标注配对")
                return None
            
            # 2. 分层分割
            split_data = self.stratified_split(valid_pairs)
            
            # 3. 创建目录结构
            self.create_directory_structure()
            
            # 4. 复制文件
            self.copy_files(split_data)
            
            # 5. 创建配置文件
            config = self.create_yaml_config(split_data)
            
            # 6. 生成可视化
            chart_file = self.visualize_distribution(split_data, stats)
            
            # 7. 保存分割信息
            split_info = {
                'split_config': {
                    'train_ratio': self.train_ratio,
                    'val_ratio': self.val_ratio,
                    'test_ratio': self.test_ratio,
                    'seed': self.seed
                },
                'statistics': stats,
                'split_data': {
                    'train': len(split_data['train']),
                    'val': len(split_data['val']),
                    'test': len(split_data['test'])
                },
                'dataset_config': config
            }
            
            info_file = self.output_dir / 'split_info.json'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(split_info, f, indent=2, ensure_ascii=False, default=str)
            
            print("\n" + "=" * 50)
            print("✅ 数据集分割完成！")
            print(f"📁 输出目录: {self.output_dir}")
            print(f"📊 分割信息: {info_file}")
            print(f"📈 可视化图表: {chart_file}")
            print(f"⚙️  YOLO配置: {self.output_dir / 'dataset.yaml'}")
            
            return split_info
            
        except Exception as e:
            print(f"❌ 分割过程中出现错误: {e}")
            return None

if __name__ == "__main__":
    # 创建数据集分割器
    splitter = DatasetSplitter(
        data_dir="data",
        output_dir="dataset",
        train_ratio=0.7,
        val_ratio=0.2,
        test_ratio=0.1,
        seed=42
    )
    
    # 运行分割
    result = splitter.run_split()
