#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 标注可视化工具
显示原图和标注后的图像对比
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import random
from typing import List, Tuple, Dict
import argparse

class AnnotationVisualizer:
    """标注可视化器"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.images_dir = self.data_dir
        self.labels_dir = self.data_dir / "labels"
        self.output_dir = Path("annotation_visualizations")
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"🎨 标注可视化器初始化")
        print(f"   图像目录: {self.images_dir}")
        print(f"   标签目录: {self.labels_dir}")
        print(f"   输出目录: {self.output_dir}")
    
    def load_annotations(self, label_file: Path) -> List[Tuple[float, float, float, float]]:
        """加载YOLO格式的标注"""
        annotations = []
        if label_file.exists():
            with open(label_file, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        # YOLO格式: class x_center y_center width height
                        cls, x, y, w, h = map(float, parts[:5])
                        annotations.append((x, y, w, h))
        return annotations
    
    def yolo_to_bbox(self, x_center: float, y_center: float, width: float, height: float, 
                     img_width: int, img_height: int) -> Tuple[int, int, int, int]:
        """将YOLO格式转换为边界框坐标"""
        x_center *= img_width
        y_center *= img_height
        width *= img_width
        height *= img_height
        
        x1 = int(x_center - width / 2)
        y1 = int(y_center - height / 2)
        x2 = int(x_center + width / 2)
        y2 = int(y_center + height / 2)
        
        # 确保坐标在图像范围内
        x1 = max(0, min(x1, img_width))
        y1 = max(0, min(y1, img_height))
        x2 = max(0, min(x2, img_width))
        y2 = max(0, min(y2, img_height))
        
        return x1, y1, x2, y2
    
    def create_side_by_side_visualization(self, image_path: Path, annotations: List[Tuple], 
                                        save_path: Path) -> Dict:
        """创建原图和标注图的并排对比"""
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            return {"error": "无法读取图像"}
        
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        img_height, img_width = image.shape[:2]
        
        # 创建标注图像
        annotated_image = image.copy()
        
        # 定义颜色
        colors = [
            (255, 0, 0),    # 红色
            (0, 255, 0),    # 绿色
            (0, 0, 255),    # 蓝色
            (255, 255, 0),  # 黄色
            (255, 0, 255),  # 紫色
            (0, 255, 255),  # 青色
        ]
        
        # 绘制每个标注框
        for i, (x, y, w, h) in enumerate(annotations):
            x1, y1, x2, y2 = self.yolo_to_bbox(x, y, w, h, img_width, img_height)
            
            # 选择颜色
            color = colors[i % len(colors)]
            
            # 绘制边界框
            cv2.rectangle(annotated_image, (x1, y1), (x2, y2), color, 3)
            
            # 绘制中心点
            center_x = int((x1 + x2) / 2)
            center_y = int((y1 + y2) / 2)
            cv2.circle(annotated_image, (center_x, center_y), 5, color, -1)
            
            # 添加编号
            cv2.putText(annotated_image, f'{i+1}', (x1, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)
            
            # 添加面积信息
            area = (x2 - x1) * (y2 - y1)
            cv2.putText(annotated_image, f'{area}px²', (x1, y2+25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 创建并排显示
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
        
        # 原图
        ax1.imshow(image)
        ax1.set_title(f'原图: {image_path.name}', fontsize=14, fontweight='bold')
        ax1.axis('off')
        
        # 标注图
        ax2.imshow(annotated_image)
        ax2.set_title(f'标注结果: 检测到 {len(annotations)} 朵花', fontsize=14, fontweight='bold')
        ax2.axis('off')
        
        # 添加统计信息
        stats_text = f"图像尺寸: {img_width}×{img_height}\n"
        stats_text += f"检测数量: {len(annotations)}\n"
        if annotations:
            areas = [(w * img_width) * (h * img_height) for _, _, w, h in annotations]
            stats_text += f"平均面积: {np.mean(areas):.0f}px²\n"
            stats_text += f"面积范围: {min(areas):.0f}-{max(areas):.0f}px²\n"
            stats_text += f"密度: {len(annotations)/(img_width*img_height)*1000000:.2f} 朵/MP"
        
        plt.figtext(0.02, 0.02, stats_text, fontsize=10, 
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        return {
            "image_size": (img_width, img_height),
            "flower_count": len(annotations),
            "areas": [(w * img_width) * (h * img_height) for _, _, w, h in annotations] if annotations else [],
            "visualization_saved": str(save_path)
        }
    
    def create_detailed_visualization(self, image_path: Path, annotations: List[Tuple], 
                                    save_path: Path) -> Dict:
        """创建详细的标注可视化"""
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            return {"error": "无法读取图像"}
        
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        img_height, img_width = image.shape[:2]
        
        # 创建多个子图
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 原图
        ax1 = plt.subplot(2, 3, 1)
        plt.imshow(image)
        plt.title(f'原图\n{image_path.name}', fontsize=12, fontweight='bold')
        plt.axis('off')
        
        # 2. 标注图
        annotated_image = image.copy()
        colors = plt.cm.Set3(np.linspace(0, 1, max(len(annotations), 1)))
        
        for i, (x, y, w, h) in enumerate(annotations):
            x1, y1, x2, y2 = self.yolo_to_bbox(x, y, w, h, img_width, img_height)
            color = (np.array(colors[i % len(colors)][:3]) * 255).astype(int)
            
            cv2.rectangle(annotated_image, (x1, y1), (x2, y2), color.tolist(), 3)
            cv2.putText(annotated_image, f'{i+1}', (x1, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, color.tolist(), 2)
        
        ax2 = plt.subplot(2, 3, 2)
        plt.imshow(annotated_image)
        plt.title(f'标注结果\n检测到 {len(annotations)} 朵花', fontsize=12, fontweight='bold')
        plt.axis('off')
        
        # 3. 热力图
        ax3 = plt.subplot(2, 3, 3)
        heatmap = np.zeros((img_height, img_width))
        for x, y, w, h in annotations:
            x1, y1, x2, y2 = self.yolo_to_bbox(x, y, w, h, img_width, img_height)
            heatmap[y1:y2, x1:x2] += 1
        
        plt.imshow(heatmap, cmap='hot', alpha=0.7)
        plt.title('密度热力图', fontsize=12, fontweight='bold')
        plt.axis('off')
        plt.colorbar(shrink=0.6)
        
        # 4. 面积分布
        if annotations:
            ax4 = plt.subplot(2, 3, 4)
            areas = [(w * img_width) * (h * img_height) for _, _, w, h in annotations]
            plt.hist(areas, bins=min(10, len(areas)), alpha=0.7, color='skyblue', edgecolor='black')
            plt.xlabel('面积 (px²)')
            plt.ylabel('数量')
            plt.title('花朵面积分布', fontsize=12, fontweight='bold')
            plt.grid(True, alpha=0.3)
        
        # 5. 位置分布
        if annotations:
            ax5 = plt.subplot(2, 3, 5)
            x_centers = [x * img_width for x, _, _, _ in annotations]
            y_centers = [y * img_height for _, y, _, _ in annotations]
            plt.scatter(x_centers, y_centers, alpha=0.7, s=50, c='red')
            plt.xlim(0, img_width)
            plt.ylim(img_height, 0)  # 翻转Y轴
            plt.xlabel('X坐标')
            plt.ylabel('Y坐标')
            plt.title('花朵中心位置分布', fontsize=12, fontweight='bold')
            plt.grid(True, alpha=0.3)
        
        # 6. 统计信息
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('off')
        
        stats_text = f"图像信息:\n"
        stats_text += f"• 文件名: {image_path.name}\n"
        stats_text += f"• 尺寸: {img_width}×{img_height}\n"
        stats_text += f"• 总像素: {img_width*img_height:,}\n\n"
        
        stats_text += f"检测结果:\n"
        stats_text += f"• 花朵数量: {len(annotations)}\n"
        
        if annotations:
            areas = [(w * img_width) * (h * img_height) for _, _, w, h in annotations]
            stats_text += f"• 平均面积: {np.mean(areas):.0f}px²\n"
            stats_text += f"• 面积范围: {min(areas):.0f}-{max(areas):.0f}px²\n"
            stats_text += f"• 面积标准差: {np.std(areas):.0f}px²\n"
            stats_text += f"• 密度: {len(annotations)/(img_width*img_height)*1000000:.2f} 朵/MP\n"
            
            # 计算覆盖率
            total_area = sum(areas)
            coverage = total_area / (img_width * img_height) * 100
            stats_text += f"• 覆盖率: {coverage:.2f}%"
        
        ax6.text(0.1, 0.9, stats_text, transform=ax6.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        return {
            "image_size": (img_width, img_height),
            "flower_count": len(annotations),
            "areas": [(w * img_width) * (h * img_height) for _, _, w, h in annotations] if annotations else [],
            "visualization_saved": str(save_path)
        }
    
    def visualize_samples(self, num_samples: int = 5, detailed: bool = True) -> List[Dict]:
        """可视化随机样本"""
        print(f"\n🎨 可视化 {num_samples} 个随机样本...")
        
        image_files = list(self.images_dir.glob("*.JPG")) + list(self.images_dir.glob("*.jpg"))
        
        if len(image_files) == 0:
            print("❌ 没有找到图像文件")
            return []
        
        # 随机选择样本
        sample_files = random.sample(image_files, min(num_samples, len(image_files)))
        
        results = []
        
        for i, img_file in enumerate(sample_files):
            print(f"   处理样本 {i+1}/{len(sample_files)}: {img_file.name}")
            
            label_file = self.labels_dir / f"{img_file.stem}.txt"
            annotations = self.load_annotations(label_file)
            
            if detailed:
                # 详细可视化
                vis_path = self.output_dir / f"detailed_{i+1:02d}_{img_file.stem}.jpg"
                result = self.create_detailed_visualization(img_file, annotations, vis_path)
            else:
                # 简单对比
                vis_path = self.output_dir / f"comparison_{i+1:02d}_{img_file.stem}.jpg"
                result = self.create_side_by_side_visualization(img_file, annotations, vis_path)
            
            result["image_name"] = img_file.name
            result["sample_index"] = i + 1
            
            results.append(result)
        
        return results
    
    def visualize_all_annotations(self, max_images: int = 20) -> Dict:
        """可视化所有标注（或前N个）"""
        print(f"\n🎨 可视化所有标注（最多{max_images}张）...")
        
        image_files = list(self.images_dir.glob("*.JPG")) + list(self.images_dir.glob("*.jpg"))
        
        if len(image_files) == 0:
            print("❌ 没有找到图像文件")
            return {}
        
        # 限制数量
        if len(image_files) > max_images:
            image_files = image_files[:max_images]
            print(f"   限制为前{max_images}张图像")
        
        results = []
        total_flowers = 0
        
        for i, img_file in enumerate(image_files):
            print(f"   处理 {i+1}/{len(image_files)}: {img_file.name}")
            
            label_file = self.labels_dir / f"{img_file.stem}.txt"
            annotations = self.load_annotations(label_file)
            
            # 创建简单对比图
            vis_path = self.output_dir / f"all_{i+1:03d}_{img_file.stem}.jpg"
            result = self.create_side_by_side_visualization(img_file, annotations, vis_path)
            result["image_name"] = img_file.name
            
            total_flowers += len(annotations)
            results.append(result)
        
        summary = {
            "total_images": len(image_files),
            "total_flowers": total_flowers,
            "avg_flowers_per_image": total_flowers / len(image_files) if image_files else 0,
            "results": results
        }
        
        return summary

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='FlowerCount-YOLO 标注可视化工具')
    parser.add_argument('--mode', choices=['samples', 'all'], default='samples',
                       help='可视化模式: samples(随机样本) 或 all(所有图像)')
    parser.add_argument('--num_samples', type=int, default=5,
                       help='随机样本数量 (默认: 5)')
    parser.add_argument('--detailed', action='store_true',
                       help='创建详细的可视化图表')
    parser.add_argument('--max_images', type=int, default=20,
                       help='all模式下的最大图像数量 (默认: 20)')
    
    args = parser.parse_args()
    
    print("🌸 FlowerCount-YOLO 标注可视化工具")
    print("=" * 60)
    
    # 创建可视化器
    visualizer = AnnotationVisualizer()
    
    if args.mode == 'samples':
        # 可视化随机样本
        results = visualizer.visualize_samples(args.num_samples, args.detailed)
        
        print(f"\n📊 可视化结果:")
        total_flowers = sum(r.get('flower_count', 0) for r in results)
        print(f"   处理图像: {len(results)}")
        print(f"   总花朵数: {total_flowers}")
        print(f"   平均每图: {total_flowers/len(results):.1f} 朵" if results else "   平均每图: 0 朵")
        
    elif args.mode == 'all':
        # 可视化所有标注
        summary = visualizer.visualize_all_annotations(args.max_images)
        
        print(f"\n📊 可视化结果:")
        print(f"   处理图像: {summary.get('total_images', 0)}")
        print(f"   总花朵数: {summary.get('total_flowers', 0)}")
        print(f"   平均每图: {summary.get('avg_flowers_per_image', 0):.1f} 朵")
    
    print(f"\n📁 可视化结果保存在: annotation_visualizations/")
    print("✅ 标注可视化完成！")

if __name__ == "__main__":
    main()
