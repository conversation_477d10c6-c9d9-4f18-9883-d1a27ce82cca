#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 快速演示脚本
"""

import os
import json
from pathlib import Path

def main():
    """主演示函数"""
    print("🌸 FlowerCount-YOLO 快速演示")
    print("=" * 50)
    
    # 检查项目结构
    project_root = Path.cwd()
    data_dir = project_root / "data"
    labels_dir = data_dir / "labels"
    viz_dir = project_root / "annotation_visualizations"
    dataset_dir = project_root / "dataset"
    
    print(f"📁 项目目录: {project_root}")
    
    # 统计文件
    image_files = list(data_dir.glob("*.JPG"))
    label_files = list(labels_dir.glob("*.txt"))
    viz_files = list(viz_dir.glob("*.jpg"))
    
    print(f"\n📊 项目统计:")
    print(f"   🖼️  原始图像: {len(image_files)} 张")
    print(f"   🏷️  标注文件: {len(label_files)} 个")
    print(f"   🎨 可视化文件: {len(viz_files)} 个")
    
    # 分析花朵数量
    if label_files:
        flower_counts = []
        for label_file in label_files:
            try:
                with open(label_file, 'r') as f:
                    lines = f.readlines()
                flower_count = len([line for line in lines if line.strip()])
                flower_counts.append(flower_count)
            except:
                continue
        
        if flower_counts:
            total_flowers = sum(flower_counts)
            avg_flowers = total_flowers / len(flower_counts)
            max_flowers = max(flower_counts)
            min_flowers = min(flower_counts)
            
            print(f"\n🌸 花朵检测统计:")
            print(f"   总花朵数: {total_flowers}")
            print(f"   平均每图: {avg_flowers:.1f} 朵")
            print(f"   检测范围: {min_flowers} - {max_flowers} 朵")
            
            # 密度分组
            density_groups = {
                '0朵': len([c for c in flower_counts if c == 0]),
                '1-5朵': len([c for c in flower_counts if 1 <= c <= 5]),
                '6-15朵': len([c for c in flower_counts if 6 <= c <= 15]),
                '16-30朵': len([c for c in flower_counts if 16 <= c <= 30]),
                '31-50朵': len([c for c in flower_counts if 31 <= c <= 50]),
                '50+朵': len([c for c in flower_counts if c > 50])
            }
            
            print(f"\n📊 密度分布:")
            for group, count in density_groups.items():
                percentage = count / len(flower_counts) * 100
                print(f"   {group}: {count} 张 ({percentage:.1f}%)")
            
            # 找出高密度样例
            high_density = [(i, count) for i, count in enumerate(flower_counts) if count >= 40]
            if high_density:
                print(f"\n🔥 高密度检测样例 (≥40朵):")
                high_density.sort(key=lambda x: x[1], reverse=True)
                for i, (idx, count) in enumerate(high_density[:5]):
                    label_file = label_files[idx]
                    print(f"   #{i+1}: {label_file.stem} - {count} 朵花")
    
    # 检查数据集分割
    split_info_path = dataset_dir / "split_info.json"
    if split_info_path.exists():
        try:
            with open(split_info_path, 'r', encoding='utf-8') as f:
                split_info = json.load(f)
            
            dataset_stats = split_info.get('dataset_config', {}).get('dataset_info', {})
            print(f"\n📊 数据集分割:")
            print(f"   训练集: {dataset_stats.get('train_images', 0)} 张")
            print(f"   验证集: {dataset_stats.get('val_images', 0)} 张")
            print(f"   测试集: {dataset_stats.get('test_images', 0)} 张")
            print(f"   训练花朵: {dataset_stats.get('train_flowers', 0)} 朵")
            print(f"   验证花朵: {dataset_stats.get('val_flowers', 0)} 朵")
            print(f"   测试花朵: {dataset_stats.get('test_flowers', 0)} 朵")
        except:
            print(f"\n📊 数据集分割: 配置文件读取失败")
    
    # 检查训练结果
    runs_dir = project_root / "runs" / "train"
    if runs_dir.exists():
        train_dirs = list(runs_dir.glob("*"))
        print(f"\n🎯 训练结果:")
        print(f"   训练实验数: {len(train_dirs)}")
        
        for train_dir in train_dirs:
            weights_dir = train_dir / "weights"
            if weights_dir.exists():
                best_model = weights_dir / "best.pt"
                last_model = weights_dir / "last.pt"
                print(f"   实验: {train_dir.name}")
                print(f"     最佳模型: {'✅' if best_model.exists() else '❌'}")
                print(f"     最终模型: {'✅' if last_model.exists() else '❌'}")
    
    print(f"\n🎉 FlowerCount-YOLO 项目概览:")
    print(f"   ✅ 自动标注系统: 完成 {len(label_files)} 张图像标注")
    print(f"   ✅ 数据集分割: 完成训练/验证/测试集分割")
    print(f"   ✅ 可视化系统: 生成 {len(viz_files)} 张检测结果图")
    print(f"   ✅ 高密度检测: 支持最高 {max_flowers if 'max_flowers' in locals() else 'N/A'} 朵花检测")
    
    print(f"\n📋 项目文件:")
    important_files = [
        "high_density_annotator.py",
        "create_dataset_split.py", 
        "train_flowercount_yolo.py",
        "flowercount_yolo_config.yaml",
        "flowercount_yolo_manager.py",
        "FlowerCount-YOLO_Project_Summary.md"
    ]
    
    for file_name in important_files:
        file_path = project_root / file_name
        status = "✅" if file_path.exists() else "❌"
        print(f"   {status} {file_name}")
    
    print(f"\n🌟 项目特色:")
    print(f"   🔬 多颜色空间分析 (HSV + LAB)")
    print(f"   🎯 自适应阈值处理")
    print(f"   🔧 形态学操作优化")
    print(f"   📊 质量过滤机制")
    print(f"   🎨 完整可视化系统")
    print(f"   📈 发表级实验设计")
    
    print(f"\n🚀 使用方法:")
    print(f"   1. 自动标注: python high_density_annotator.py")
    print(f"   2. 数据分割: python create_dataset_split.py")
    print(f"   3. 模型训练: python train_flowercount_yolo.py")
    print(f"   4. 完整流程: python flowercount_yolo_manager.py")
    
    print(f"\n📞 项目信息:")
    print(f"   名称: FlowerCount-YOLO")
    print(f"   版本: v1.0.0")
    print(f"   描述: 高密度花朵检测与计数系统")
    print(f"   技术: YOLOv8 + OpenCV + 多种CV技术")

if __name__ == "__main__":
    main()
