#!/usr/bin/env python3
"""
Complete FlowerCount-YOLO Experiment Runner

This script demonstrates the complete experimental pipeline for the research paper:
"FlowerCount-YOLO: A Novel Multi-Scale Attention-Enhanced Framework 
for Accurate Flower Detection and Counting"

This script will:
1. Analyze your flower dataset
2. Generate automatic annotations using SAM2+CLIP
3. Train the FlowerCount-YOLO model with progressive strategy
4. Evaluate the model with comprehensive metrics
5. Run ablation studies
6. Compare with SOTA methods
7. Generate publication-ready visualizations
8. Compile results for the research paper

Usage:
    python run_complete_experiment.py
"""

import os
import sys
import time
import logging
from pathlib import Path
from datetime import datetime
import json
import numpy as np

# Setup paths
PROJECT_ROOT = Path(__file__).parent
sys.path.append(str(PROJECT_ROOT / 'src'))

# Import our modules
try:
    from main_experiment import FlowerCountExperiment
    from quick_start import setup_logging, check_data_directory
    from src.visualization.model_architecture import ModelArchitectureVisualizer
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed.")
    sys.exit(1)


def print_banner():
    """Print experiment banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                        FlowerCount-YOLO Experiment                          ║
║                                                                              ║
║    🌸 Advanced Flower Detection and Counting System                         ║
║    📄 Research Paper Implementation                                          ║
║    🏆 State-of-the-Art Performance                                          ║
║                                                                              ║
║    Features:                                                                 ║
║    • 🤖 Automatic annotation with SAM2+CLIP                                ║
║    • 🧠 Multi-scale attention mechanisms                                    ║
║    • 📊 Density estimation for accurate counting                            ║
║    • 🚀 Progressive training strategy                                       ║
║    • 📈 Comprehensive evaluation and analysis                               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_requirements():
    """Check if all requirements are met"""
    
    print("🔍 Checking requirements...")
    
    # Check Python version
    import sys
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # Check PyTorch
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
        
        # Check CUDA availability
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name()}")
        else:
            print("⚠️  CUDA not available, using CPU")
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    
    # Check data directory
    if not check_data_directory("data"):
        print("❌ Data directory check failed")
        print("📁 Please place your flower images in the 'data/' directory")
        return False
    
    print("✅ All requirements met!")
    return True


def estimate_experiment_time():
    """Estimate total experiment time"""
    
    # Count images
    data_path = Path("data")
    image_count = len(list(data_path.glob('*.jpg'))) + \
                 len(list(data_path.glob('*.JPG'))) + \
                 len(list(data_path.glob('*.png'))) + \
                 len(list(data_path.glob('*.PNG')))
    
    # Estimate times (in minutes)
    annotation_time = max(5, image_count * 0.5)  # ~30 seconds per image
    training_time = 180  # ~3 hours for full training
    evaluation_time = 15  # ~15 minutes
    ablation_time = 60   # ~1 hour for ablation studies
    visualization_time = 10  # ~10 minutes
    
    total_time = annotation_time + training_time + evaluation_time + ablation_time + visualization_time
    
    print(f"⏱️  Estimated experiment time:")
    print(f"   📊 Dataset analysis: 2 minutes")
    print(f"   🏷️  Auto-annotation: {annotation_time:.0f} minutes")
    print(f"   🚀 Model training: {training_time:.0f} minutes")
    print(f"   📏 Evaluation: {evaluation_time:.0f} minutes")
    print(f"   🧪 Ablation studies: {ablation_time:.0f} minutes")
    print(f"   🎨 Visualizations: {visualization_time:.0f} minutes")
    print(f"   ⏰ Total: {total_time:.0f} minutes ({total_time/60:.1f} hours)")
    
    return total_time


def create_experiment_summary():
    """Create experiment summary"""
    
    summary = {
        "experiment_info": {
            "name": "FlowerCount-YOLO Complete Experiment",
            "start_time": datetime.now().isoformat(),
            "description": "Complete experimental pipeline for flower detection and counting research",
            "paper_title": "FlowerCount-YOLO: A Novel Multi-Scale Attention-Enhanced Framework for Accurate Flower Detection and Counting"
        },
        "system_info": {
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "platform": sys.platform,
            "working_directory": str(Path.cwd())
        },
        "dataset_info": {
            "data_path": "data/",
            "image_count": len(list(Path("data").glob('*.jpg'))) + len(list(Path("data").glob('*.JPG')))
        }
    }
    
    # Save summary
    with open("experiment_summary.json", 'w') as f:
        json.dump(summary, f, indent=2)
    
    return summary


def run_step(step_name, step_function, *args, **kwargs):
    """Run a single experiment step with timing and error handling"""
    
    print(f"\n{'='*60}")
    print(f"🚀 Starting: {step_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = step_function(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ {step_name} completed successfully!")
        print(f"⏱️  Duration: {duration/60:.1f} minutes")
        
        return result, True
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ {step_name} failed!")
        print(f"🐛 Error: {e}")
        print(f"⏱️  Duration: {duration/60:.1f} minutes")
        
        return None, False


def main():
    """Main experiment runner"""
    
    # Print banner
    print_banner()
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting complete FlowerCount-YOLO experiment")
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        return
    
    # Estimate time
    print(f"\n📊 Experiment Overview:")
    estimated_time = estimate_experiment_time()
    
    # Ask for confirmation
    print(f"\n❓ This experiment will take approximately {estimated_time/60:.1f} hours.")
    response = input("Do you want to continue? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("🛑 Experiment cancelled by user.")
        return
    
    # Create experiment summary
    summary = create_experiment_summary()
    print(f"\n📋 Experiment summary saved to: experiment_summary.json")
    
    # Initialize experiment
    print(f"\n🏗️  Initializing experiment...")
    
    try:
        experiment = FlowerCountExperiment("configs/experiment_config.yaml")
    except FileNotFoundError:
        print("⚠️  Configuration file not found, using default settings...")
        # Create a minimal config for demo
        config_dir = Path("configs")
        config_dir.mkdir(exist_ok=True)
        
        minimal_config = {
            "experiment": {"name": "FlowerCount-YOLO-Demo"},
            "dataset": {"path": "data/", "auto_annotation": {"enabled": True}},
            "training": {"epochs": 50, "batch_size": 8},
            "hardware": {"device": "auto"}
        }
        
        with open("configs/experiment_config.yaml", 'w') as f:
            import yaml
            yaml.dump(minimal_config, f)
        
        experiment = FlowerCountExperiment("configs/experiment_config.yaml")
    
    # Track experiment progress
    steps_completed = 0
    total_steps = 7
    
    # Step 1: Data Preparation and Auto-annotation
    result, success = run_step(
        "Data Preparation and Auto-annotation",
        experiment.prepare_dataset
    )
    if success:
        steps_completed += 1
    
    # Step 2: Model Training
    result, success = run_step(
        "Model Training",
        experiment.train_model
    )
    if success:
        steps_completed += 1
    
    # Step 3: Model Evaluation
    result, success = run_step(
        "Model Evaluation", 
        experiment.evaluate_model
    )
    if success:
        steps_completed += 1
    
    # Step 4: Ablation Studies
    result, success = run_step(
        "Ablation Studies",
        experiment.run_ablation_studies
    )
    if success:
        steps_completed += 1
    
    # Step 5: Comparison Experiments
    result, success = run_step(
        "Comparison Experiments",
        experiment.run_comparison_experiments
    )
    if success:
        steps_completed += 1
    
    # Step 6: Visualization and Analysis
    result, success = run_step(
        "Visualization and Analysis",
        experiment.generate_visualizations
    )
    if success:
        steps_completed += 1
    
    # Step 7: Results Compilation
    result, success = run_step(
        "Results Compilation",
        experiment.compile_results
    )
    if success:
        steps_completed += 1
    
    # Final summary
    print(f"\n{'='*60}")
    print(f"🎉 EXPERIMENT COMPLETED!")
    print(f"{'='*60}")
    
    print(f"📊 Progress: {steps_completed}/{total_steps} steps completed")
    
    if steps_completed == total_steps:
        print(f"✅ All steps completed successfully!")
        print(f"🏆 FlowerCount-YOLO experiment finished!")
    else:
        print(f"⚠️  {total_steps - steps_completed} steps had issues")
        print(f"📝 Check the logs for details")
    
    # Show results location
    results_dir = experiment.experiment_dir
    print(f"\n📁 Results saved to: {results_dir}")
    print(f"📄 Key files:")
    print(f"   • Training results: {results_dir}/training/")
    print(f"   • Evaluation metrics: {results_dir}/evaluation/")
    print(f"   • Visualizations: {results_dir}/visualizations/")
    print(f"   • Publication results: {results_dir}/publication_results/")
    
    # Generate final architecture diagrams
    print(f"\n🎨 Generating final architecture diagrams...")
    try:
        visualizer = ModelArchitectureVisualizer(style='paper')
        arch_dir = results_dir / "architecture_diagrams"
        visualizer.generate_all_diagrams(str(arch_dir))
        print(f"📊 Architecture diagrams saved to: {arch_dir}")
    except Exception as e:
        print(f"⚠️  Architecture diagram generation failed: {e}")
    
    # Final message
    print(f"\n🌸 Thank you for using FlowerCount-YOLO!")
    print(f"📚 Don't forget to cite our paper if you use this work!")
    print(f"⭐ Star our repository if you found it useful!")
    
    # Update experiment summary
    summary["experiment_info"]["end_time"] = datetime.now().isoformat()
    summary["experiment_info"]["steps_completed"] = steps_completed
    summary["experiment_info"]["total_steps"] = total_steps
    summary["experiment_info"]["success_rate"] = f"{steps_completed/total_steps*100:.1f}%"
    
    with open("experiment_summary.json", 'w') as f:
        json.dump(summary, f, indent=2)


if __name__ == "__main__":
    main()
