@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                   FlowerCount-YOLO CUDA Installation                        ║
echo ║                        (Administrator Mode)                                 ║
echo ║                                                                              ║
echo ║    🌸 Advanced Flower Detection and Counting System                         ║
echo ║    🔥 Installing PyTorch CUDA Support                                       ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running with Administrator privileges
) else (
    echo ❌ This script must be run as Administrator!
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo 🔍 Checking conda installation...
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Conda not found!
    echo Please install Anaconda or Miniconda first
    pause
    exit /b 1
)
echo ✅ Conda found!

echo.
echo 📋 Available conda environments:
conda env list

echo.
echo 🎯 Activating flowercount-yolo-cuda environment...
call conda activate flowercount-yolo-cuda
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to activate environment!
    echo Creating new environment...
    conda create -n flowercount-yolo-cuda python=3.9 -y
    call conda activate flowercount-yolo-cuda
)

echo.
echo 🔥 Installing PyTorch with CUDA 11.8 support...
echo This may take several minutes...

REM Method 1: Try conda install first
echo 📦 Trying conda installation...
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
if %ERRORLEVEL% == 0 (
    echo ✅ PyTorch installed successfully via conda!
    goto :verify_installation
)

echo ⚠️  Conda installation failed, trying pip...

REM Method 2: Try pip install
echo 📦 Trying pip installation...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
if %ERRORLEVEL% == 0 (
    echo ✅ PyTorch installed successfully via pip!
    goto :verify_installation
)

echo ❌ Both installation methods failed!
goto :installation_failed

:verify_installation
echo.
echo 🧪 Verifying PyTorch CUDA installation...
python -c "import torch; print('PyTorch version:', torch.__version__); print('CUDA available:', torch.cuda.is_available()); print('CUDA version:', torch.version.cuda if torch.cuda.is_available() else 'None'); print('GPU count:', torch.cuda.device_count() if torch.cuda.is_available() else 0); print('GPU name:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'None')"

if %ERRORLEVEL% == 0 (
    echo ✅ PyTorch verification successful!
    goto :install_dependencies
) else (
    echo ❌ PyTorch verification failed!
    goto :installation_failed
)

:install_dependencies
echo.
echo 📦 Installing additional dependencies...

REM Install essential packages
pip install ultralytics>=8.0.0
pip install opencv-python>=4.8.0
pip install matplotlib>=3.7.0
pip install seaborn>=0.12.0
pip install pandas>=2.0.0
pip install numpy>=1.24.0
pip install scipy>=1.11.0
pip install scikit-learn>=1.3.0
pip install tqdm>=4.65.0
pip install pyyaml>=6.0
pip install pillow>=10.0.0

echo.
echo 🎯 Installing optional advanced packages...
pip install wandb
pip install tensorboard
pip install grad-cam
pip install transformers

echo.
echo 🧪 Final verification...
python -c "
import torch
import torchvision
import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import sklearn
import yaml
from PIL import Image
import tqdm

print('✅ All core packages imported successfully!')
print('🔥 PyTorch CUDA:', torch.cuda.is_available())
print('🎯 Ready for FlowerCount-YOLO!')
"

if %ERRORLEVEL__ == 0 (
    echo.
    echo 🎉 Installation completed successfully!
    echo.
    echo 📋 Next steps:
    echo    1. Keep this terminal open
    echo    2. Run: python start_project.py
    echo    3. Or run: python quick_start.py --mode demo
    echo.
    goto :success
) else (
    echo ❌ Final verification failed!
    goto :installation_failed
)

:installation_failed
echo.
echo ❌ Installation failed!
echo.
echo 🔧 Troubleshooting steps:
echo    1. Make sure you're running as Administrator
echo    2. Check your internet connection
echo    3. Try running the commands manually:
echo       conda activate flowercount-yolo-cuda
echo       conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
echo.
pause
exit /b 1

:success
echo.
echo 🌸 FlowerCount-YOLO CUDA environment is ready!
echo 🚀 You can now run the project with GPU acceleration!
echo.
pause
