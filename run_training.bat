@echo off
echo 🌸 FlowerCount-YOLO 训练开始
echo ========================================

cd /d "D:\Code\YOLOv10_Project"

call conda activate flowercount-yolo-cuda

echo 🔍 检查环境...
python -c "import torch; print('PyTorch:', torch.__version__); from ultralytics import YOLO; print('YOLO可用')"

echo 🚀 开始训练...
python -c "
from ultralytics import YOLO
import time

print('创建YOLO模型...')
model = YOLO('yolov8n.pt')

print('开始训练...')
results = model.train(
    data='dataset/dataset.yaml',
    epochs=20,
    imgsz=640,
    batch=2,
    device='auto',
    workers=2,
    patience=10,
    save=True,
    val=True,
    plots=True,
    verbose=True,
    project='runs/train',
    name='flowercount_batch',
    exist_ok=True
)

print('训练完成！')
print(f'模型保存在: {results.save_dir}')
"

echo 🎉 训练流程完成！
pause
