#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 简化训练脚本
快速开始训练高密度花朵检测模型
"""

import os
import sys
from pathlib import Path
import yaml

def main():
    """主训练函数"""
    print("🌸 FlowerCount-YOLO 简化训练开始")
    print("=" * 50)
    
    try:
        # 导入必要的库
        from ultralytics import YOLO
        print("✅ 成功导入 ultralytics")
        
        # 检查数据集配置
        dataset_config_path = Path("dataset/dataset.yaml")
        if not dataset_config_path.exists():
            print("❌ 数据集配置文件不存在，请先运行数据集分割")
            return False
        
        print(f"✅ 找到数据集配置: {dataset_config_path}")
        
        # 读取数据集配置
        with open(dataset_config_path, 'r', encoding='utf-8') as f:
            dataset_config = yaml.safe_load(f)
        
        print(f"📊 数据集信息:")
        print(f"   - 训练图像: {dataset_config['dataset_info']['train_images']}")
        print(f"   - 验证图像: {dataset_config['dataset_info']['val_images']}")
        print(f"   - 测试图像: {dataset_config['dataset_info']['test_images']}")
        print(f"   - 总花朵数: {dataset_config['dataset_info']['total_flowers']}")
        
        # 创建YOLO模型
        print("\n🎯 创建YOLO模型...")
        model = YOLO('yolov8n.pt')  # 使用预训练的YOLOv8n模型
        print("✅ 模型创建成功")
        
        # 训练参数
        train_args = {
            'data': str(dataset_config_path),
            'epochs': 50,  # 减少轮数以便快速测试
            'imgsz': 640,  # 使用较小的图像尺寸
            'batch': 4,    # 小批次大小
            'device': 'auto',
            'workers': 4,
            'patience': 10,
            'save': True,
            'save_period': 10,
            'val': True,
            'plots': True,
            'verbose': True,
            'project': 'runs/train',
            'name': 'flowercount_yolo_simple',
            'exist_ok': True,
            'pretrained': True,
            'optimizer': 'AdamW',
            'lr0': 0.001,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            'pose': 12.0,
            'kobj': 1.0,
            'label_smoothing': 0.0,
            'nbs': 64,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'val_period': 1,
            'cache': False,
            'rect': False,
            'cos_lr': False,
            'close_mosaic': 10,
            'resume': False,
            'amp': True,
            'fraction': 1.0,
            'profile': False,
            'freeze': None,
            'multi_scale': False,
            'copy_paste': 0.0,
            'auto_augment': 'randaugment',
            'erasing': 0.4,
            'crop_fraction': 1.0,
            'mixup': 0.0,
            'mosaic': 1.0,
            'degrees': 0.0,
            'translate': 0.1,
            'scale': 0.5,
            'shear': 0.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.5,
            'hsv_h': 0.015,
            'hsv_s': 0.7,
            'hsv_v': 0.4
        }
        
        print("\n🚀 开始训练...")
        print(f"📋 训练参数:")
        print(f"   - 轮数: {train_args['epochs']}")
        print(f"   - 图像尺寸: {train_args['imgsz']}")
        print(f"   - 批次大小: {train_args['batch']}")
        print(f"   - 设备: {train_args['device']}")
        print(f"   - 优化器: {train_args['optimizer']}")
        print(f"   - 学习率: {train_args['lr0']}")
        
        # 开始训练
        results = model.train(**train_args)
        
        print("\n🎉 训练完成！")
        print(f"📊 训练结果:")
        print(f"   - 最佳mAP@0.5: {results.results_dict.get('metrics/mAP50(B)', 'N/A')}")
        print(f"   - 最佳mAP@0.5:0.95: {results.results_dict.get('metrics/mAP50-95(B)', 'N/A')}")
        
        # 保存训练信息
        train_info = {
            'model_path': str(results.save_dir / 'weights' / 'best.pt'),
            'last_model_path': str(results.save_dir / 'weights' / 'last.pt'),
            'results_dir': str(results.save_dir),
            'dataset_config': dataset_config,
            'train_args': train_args,
            'final_metrics': results.results_dict if hasattr(results, 'results_dict') else {}
        }
        
        info_file = Path("training_info_simple.json")
        import json
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(train_info, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📁 训练信息保存: {info_file}")
        print(f"📁 模型保存目录: {results.save_dir}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装 ultralytics: pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 训练成功完成！")
        sys.exit(0)
    else:
        print("\n❌ 训练失败！")
        sys.exit(1)
