# Advanced Flower Detection and Counting System Configuration
# Paper: "FlowerCount-YOLO: A Novel Multi-Scale Attention-Enhanced Framework for Accurate Flower Detection and Counting"

# Experiment Settings
experiment:
  name: "FlowerCount-YOLO-v1"
  description: "Advanced flower detection and counting with multi-scale attention and density estimation"
  version: "1.0.0"
  author: "Research Team"
  
# Dataset Configuration
dataset:
  name: "FlowerDataset"
  path: "data/"
  classes: ["flower"]
  num_classes: 1
  
  # Data splits
  train_ratio: 0.7
  val_ratio: 0.2
  test_ratio: 0.1
  
  # Auto-annotation settings
  auto_annotation:
    enabled: true
    method: "SAM2_CLIP"
    confidence_threshold: 0.7
    nms_threshold: 0.5
    
  # Data augmentation
  augmentation:
    enabled: true
    mosaic: 0.5
    mixup: 0.3
    hsv_h: 0.015
    hsv_s: 0.7
    hsv_v: 0.4
    degrees: 10.0
    translate: 0.1
    scale: 0.9
    shear: 2.0
    perspective: 0.0
    flipud: 0.0
    fliplr: 0.5

# Model Architecture
model:
  name: "FlowerCount-YOLO"
  backbone: "YOLOv10"
  
  # Innovation components
  innovations:
    multi_scale_fusion: true
    attention_mechanism: "CBAM"  # Convolutional Block Attention Module
    density_estimation: true
    progressive_training: true
    
  # Architecture details
  architecture:
    input_size: [640, 640]
    depth_multiple: 1.0
    width_multiple: 1.0
    
    # Attention settings
    attention:
      type: "CBAM"
      reduction: 16
      kernel_size: 7
      
    # Multi-scale fusion
    fusion:
      type: "FPN_PAN"
      feature_levels: [3, 4, 5]
      
    # Density estimation branch
    density:
      enabled: true
      kernel_size: 3
      output_channels: 1

# Training Configuration
training:
  # Basic settings
  epochs: 300
  batch_size: 16
  learning_rate: 0.01
  momentum: 0.937
  weight_decay: 0.0005
  
  # Progressive training strategy
  progressive:
    enabled: true
    stages:
      - name: "warmup"
        epochs: 10
        lr_factor: 0.1
        freeze_backbone: true
      - name: "detection_only"
        epochs: 100
        lr_factor: 1.0
        freeze_backbone: false
        enable_density: false
      - name: "full_training"
        epochs: 190
        lr_factor: 1.0
        enable_density: true
        
  # Loss function weights
  loss_weights:
    detection: 1.0
    counting: 0.5
    density: 0.3
    
  # Optimizer settings
  optimizer:
    type: "AdamW"
    lr: 0.001
    betas: [0.9, 0.999]
    eps: 1e-8
    
  # Learning rate scheduler
  scheduler:
    type: "CosineAnnealingWarmRestarts"
    T_0: 50
    T_mult: 2
    eta_min: 1e-6

# Evaluation Metrics
evaluation:
  metrics:
    detection:
      - "mAP@0.5"
      - "mAP@0.5:0.95"
      - "Precision"
      - "Recall"
      - "F1-Score"
    counting:
      - "MAE"  # Mean Absolute Error
      - "RMSE"  # Root Mean Square Error
      - "MAPE"  # Mean Absolute Percentage Error
      - "R2"    # R-squared
    density:
      - "MSE"   # Mean Square Error
      - "SSIM"  # Structural Similarity Index
      
  # Evaluation settings
  confidence_threshold: 0.25
  nms_threshold: 0.45
  max_detections: 1000

# Visualization and Explainability
visualization:
  enabled: true
  methods:
    - "GradCAM"
    - "GradCAM++"
    - "ScoreCAM"
    - "LayerCAM"
    
  # Heatmap settings
  heatmap:
    colormap: "jet"
    alpha: 0.4
    save_format: "png"
    
  # Attention visualization
  attention_maps:
    enabled: true
    layers: ["neck", "head"]

# Experiment Tracking
tracking:
  wandb:
    enabled: true
    project: "FlowerCount-YOLO"
    entity: "research-team"
    
  tensorboard:
    enabled: true
    log_dir: "logs/tensorboard"
    
  mlflow:
    enabled: false
    tracking_uri: "http://localhost:5000"

# Hardware and Performance
hardware:
  device: "auto"  # auto, cpu, cuda, mps
  mixed_precision: true
  compile_model: true
  workers: 8
  pin_memory: true

# Reproducibility
reproducibility:
  seed: 42
  deterministic: true
  benchmark: false

# Paths
paths:
  data: "data/"
  models: "models/"
  logs: "logs/"
  results: "results/"
  checkpoints: "models/checkpoints/"
  exports: "models/exports/"
