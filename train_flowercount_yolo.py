#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 发表级训练脚本
实现渐进式训练策略和综合评估体系
"""

import os
import sys
import json
import time
import numpy as np
import torch
import torch.nn as nn
from pathlib import Path
import matplotlib.pyplot as plt
from ultralytics import YOLO
import cv2
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class FlowerCountTrainer:
    """FlowerCount-YOLO训练器"""
    
    def __init__(self, 
                 model_name: str = "yolov8n.pt",
                 data_config: str = "dataset.yaml",
                 project_name: str = "flowercount_yolo_research",
                 experiment_name: str = "publication_quality"):
        """
        初始化训练器
        
        Args:
            model_name: 基础模型名称
            data_config: 数据集配置文件
            project_name: 项目名称
            experiment_name: 实验名称
        """
        self.model_name = model_name
        self.data_config = data_config
        self.project_name = project_name
        self.experiment_name = experiment_name
        
        # 训练参数
        self.training_config = {
            'epochs': 100,
            'batch_size': 4,  # 适应高分辨率图像
            'imgsz': 1280,    # 高分辨率训练
            'patience': 20,
            'save_period': 10,
            'workers': 4,
            'device': 'auto',
            'optimizer': 'AdamW',
            'lr0': 0.001,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            'pose': 12.0,
            'kobj': 1.0,
            'label_smoothing': 0.0,
            'nbs': 64,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'val': True,
            'plots': True,
            'save_json': True,
            'save_hybrid': False,
            'conf': 0.001,
            'iou': 0.7,
            'max_det': 1000,  # 高密度场景需要更多检测
            'half': False,
            'dnn': False,
            'augment': True,
            'agnostic_nms': False,
            'retina_masks': False,
            'format': 'torchscript',
            'keras': False,
            'optimize': False,
            'int8': False,
            'dynamic': False,
            'simplify': False,
            'opset': None,
            'workspace': 4,
            'nms': False,
            'rect': False,
            'resume': False,
            'amp': True,
            'fraction': 1.0,
            'profile': False,
            'freeze': None,
            'multi_scale': True,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'cache': False,
            'close_mosaic': 10,
            'copy_paste': 0.0,
            'auto_augment': 'randaugment',
            'erasing': 0.4,
            'crop_fraction': 1.0,
        }
        
        # 创建输出目录
        self.output_dir = Path(f"runs/train/{experiment_name}")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print("🚀 FlowerCountTrainer 初始化完成")
        print(f"   📁 输出目录: {self.output_dir}")
        
    def create_dataset_config(self):
        """创建数据集配置文件"""
        print("📝 创建数据集配置...")
        
        # 检查数据目录
        data_dir = Path("data")
        if not data_dir.exists():
            raise FileNotFoundError("数据目录不存在")
        
        # 检查标注目录
        labels_dir = data_dir / "labels"
        if not labels_dir.exists():
            raise FileNotFoundError("标注目录不存在，请先运行自动标注")
        
        # 统计数据
        image_files = list(data_dir.glob("*.jpg")) + list(data_dir.glob("*.JPG"))
        label_files = list(labels_dir.glob("*.txt"))
        
        print(f"   📊 找到 {len(image_files)} 张图像")
        print(f"   🏷️  找到 {len(label_files)} 个标注文件")
        
        # 创建数据集配置
        dataset_config = {
            'path': str(Path.cwd()),
            'train': 'data',
            'val': 'data',  # 简化版本，实际应该分割数据集
            'test': 'data',
            'nc': 1,
            'names': ['flower']
        }
        
        with open(self.data_config, 'w', encoding='utf-8') as f:
            import yaml
            yaml.dump(dataset_config, f, default_flow_style=False)
        
        print(f"   ✅ 数据集配置保存: {self.data_config}")
        return dataset_config
    
    def stage1_warmup_training(self):
        """阶段1: 预热训练"""
        print("\n🔥 阶段1: 预热训练")
        print("=" * 40)
        
        # 加载模型
        model = YOLO(self.model_name)
        
        # 预热训练参数
        warmup_config = self.training_config.copy()
        warmup_config.update({
            'epochs': 20,
            'lr0': 0.0001,  # 较低学习率
            'warmup_epochs': 5,
            'patience': 10,
            'imgsz': 640,   # 较小图像尺寸
            'batch_size': 8,
            'freeze': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],  # 冻结骨干网络
        })
        
        # 开始训练
        results = model.train(
            data=self.data_config,
            project=self.project_name,
            name=f"{self.experiment_name}_stage1_warmup",
            **warmup_config
        )
        
        print("✅ 阶段1完成")
        return results
    
    def stage2_detection_training(self):
        """阶段2: 检测训练"""
        print("\n🎯 阶段2: 检测训练")
        print("=" * 40)
        
        # 加载阶段1的最佳模型
        stage1_model_path = f"runs/train/{self.experiment_name}_stage1_warmup/weights/best.pt"
        
        if not Path(stage1_model_path).exists():
            print("⚠️  阶段1模型不存在，使用预训练模型")
            model = YOLO(self.model_name)
        else:
            model = YOLO(stage1_model_path)
        
        # 检测训练参数
        detection_config = self.training_config.copy()
        detection_config.update({
            'epochs': 50,
            'lr0': 0.001,
            'imgsz': 1024,  # 中等分辨率
            'batch_size': 6,
            'patience': 15,
            'freeze': None,  # 解冻所有层
        })
        
        # 开始训练
        results = model.train(
            data=self.data_config,
            project=self.project_name,
            name=f"{self.experiment_name}_stage2_detection",
            **detection_config
        )
        
        print("✅ 阶段2完成")
        return results
    
    def stage3_full_training(self):
        """阶段3: 完整训练"""
        print("\n🌟 阶段3: 完整训练")
        print("=" * 40)
        
        # 加载阶段2的最佳模型
        stage2_model_path = f"runs/train/{self.experiment_name}_stage2_detection/weights/best.pt"
        
        if not Path(stage2_model_path).exists():
            print("⚠️  阶段2模型不存在，使用预训练模型")
            model = YOLO(self.model_name)
        else:
            model = YOLO(stage2_model_path)
        
        # 完整训练参数
        full_config = self.training_config.copy()
        full_config.update({
            'epochs': 100,
            'lr0': 0.0005,  # 精细调整学习率
            'imgsz': 1280,  # 高分辨率
            'batch_size': 4,
            'patience': 25,
            'multi_scale': True,
            'augment': True,
        })
        
        # 开始训练
        results = model.train(
            data=self.data_config,
            project=self.project_name,
            name=f"{self.experiment_name}_stage3_full",
            **full_config
        )
        
        print("✅ 阶段3完成")
        return results
    
    def evaluate_model(self, model_path: str):
        """评估模型性能"""
        print(f"\n📊 评估模型: {model_path}")
        print("=" * 40)
        
        if not Path(model_path).exists():
            print(f"❌ 模型文件不存在: {model_path}")
            return None
        
        # 加载模型
        model = YOLO(model_path)
        
        # 验证模型
        val_results = model.val(
            data=self.data_config,
            imgsz=1280,
            batch=1,
            conf=0.001,
            iou=0.6,
            max_det=1000,
            save_json=True,
            save_hybrid=False,
            plots=True,
            verbose=True
        )
        
        # 提取关键指标
        metrics = {
            'mAP50': float(val_results.box.map50) if hasattr(val_results.box, 'map50') else 0.0,
            'mAP50-95': float(val_results.box.map) if hasattr(val_results.box, 'map') else 0.0,
            'precision': float(val_results.box.mp) if hasattr(val_results.box, 'mp') else 0.0,
            'recall': float(val_results.box.mr) if hasattr(val_results.box, 'mr') else 0.0,
            'f1': 0.0,  # 需要计算
        }
        
        # 计算F1分数
        if metrics['precision'] > 0 and metrics['recall'] > 0:
            metrics['f1'] = 2 * (metrics['precision'] * metrics['recall']) / (metrics['precision'] + metrics['recall'])
        
        print(f"   📈 mAP@0.5: {metrics['mAP50']:.3f}")
        print(f"   📈 mAP@0.5:0.95: {metrics['mAP50-95']:.3f}")
        print(f"   📈 Precision: {metrics['precision']:.3f}")
        print(f"   📈 Recall: {metrics['recall']:.3f}")
        print(f"   📈 F1-Score: {metrics['f1']:.3f}")
        
        return metrics
    
    def test_inference(self, model_path: str, test_images: List[str] = None):
        """测试推理性能"""
        print(f"\n🧪 测试推理: {model_path}")
        print("=" * 40)
        
        if not Path(model_path).exists():
            print(f"❌ 模型文件不存在: {model_path}")
            return None
        
        # 加载模型
        model = YOLO(model_path)
        
        # 选择测试图像
        if test_images is None:
            data_dir = Path("data")
            all_images = list(data_dir.glob("*.jpg")) + list(data_dir.glob("*.JPG"))
            test_images = [str(img) for img in all_images[:5]]  # 测试前5张
        
        # 创建测试结果目录
        test_dir = Path("test_results_research")
        test_dir.mkdir(exist_ok=True)
        
        inference_times = []
        detection_counts = []
        
        for i, img_path in enumerate(test_images):
            print(f"   🖼️  测试图像 {i+1}/{len(test_images)}: {Path(img_path).name}")
            
            # 推理
            start_time = time.time()
            results = model(img_path, conf=0.25, iou=0.45, max_det=1000)
            inference_time = time.time() - start_time
            
            inference_times.append(inference_time)
            
            # 统计检测结果
            if results and len(results) > 0:
                detections = len(results[0].boxes) if results[0].boxes is not None else 0
                detection_counts.append(detections)
                
                # 保存结果
                result_path = test_dir / f"result_{i+1}_{Path(img_path).stem}.jpg"
                results[0].save(str(result_path))
                
                print(f"      ✅ 检测到 {detections} 朵花，用时 {inference_time:.3f}s")
            else:
                detection_counts.append(0)
                print(f"      ⚠️  未检测到花朵，用时 {inference_time:.3f}s")
        
        # 计算统计信息
        avg_inference_time = np.mean(inference_times)
        avg_detections = np.mean(detection_counts)
        total_detections = sum(detection_counts)
        
        print(f"\n📊 推理统计:")
        print(f"   ⏱️  平均推理时间: {avg_inference_time:.3f}s")
        print(f"   🌸 平均检测数: {avg_detections:.1f} 朵/图像")
        print(f"   🌸 总检测数: {total_detections} 朵")
        print(f"   📁 结果保存: {test_dir}")
        
        return {
            'avg_inference_time': avg_inference_time,
            'avg_detections': avg_detections,
            'total_detections': total_detections,
            'inference_times': inference_times,
            'detection_counts': detection_counts
        }
    
    def run_complete_training(self):
        """运行完整的三阶段训练"""
        print("🚀 开始FlowerCount-YOLO完整训练流程")
        print("=" * 60)
        
        # 创建数据集配置
        self.create_dataset_config()
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 阶段1: 预热训练
            stage1_results = self.stage1_warmup_training()
            
            # 阶段2: 检测训练
            stage2_results = self.stage2_detection_training()
            
            # 阶段3: 完整训练
            stage3_results = self.stage3_full_training()
            
            # 评估最终模型
            final_model_path = f"runs/train/{self.experiment_name}_stage3_full/weights/best.pt"
            final_metrics = self.evaluate_model(final_model_path)
            
            # 测试推理
            inference_results = self.test_inference(final_model_path)
            
            # 计算总训练时间
            total_time = time.time() - start_time
            
            # 保存训练报告
            training_report = {
                'experiment_name': self.experiment_name,
                'total_training_time': total_time,
                'final_metrics': final_metrics,
                'inference_results': inference_results,
                'training_config': self.training_config,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            report_path = self.output_dir / 'training_report.json'
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(training_report, f, indent=2, ensure_ascii=False)
            
            print("\n" + "=" * 60)
            print("🎉 FlowerCount-YOLO训练完成！")
            print(f"⏱️  总训练时间: {total_time/3600:.2f} 小时")
            print(f"📊 最终性能:")
            if final_metrics:
                print(f"   - mAP@0.5: {final_metrics['mAP50']:.3f}")
                print(f"   - mAP@0.5:0.95: {final_metrics['mAP50-95']:.3f}")
                print(f"   - F1-Score: {final_metrics['f1']:.3f}")
            if inference_results:
                print(f"   - 平均推理时间: {inference_results['avg_inference_time']:.3f}s")
                print(f"   - 平均检测数: {inference_results['avg_detections']:.1f} 朵/图像")
            print(f"📁 训练报告: {report_path}")
            
            return training_report
            
        except Exception as e:
            print(f"❌ 训练过程中出现错误: {e}")
            return None

if __name__ == "__main__":
    # 创建训练器
    trainer = FlowerCountTrainer(
        model_name="yolov8n.pt",
        data_config="dataset.yaml",
        project_name="flowercount_yolo_research",
        experiment_name="publication_quality_v1"
    )
    
    # 运行完整训练
    results = trainer.run_complete_training()
