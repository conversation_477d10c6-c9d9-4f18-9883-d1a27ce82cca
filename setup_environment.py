#!/usr/bin/env python3
"""
FlowerCount-YOLO Environment Setup Script

This script helps you set up the complete environment for FlowerCount-YOLO project.
It will guide you through creating a conda environment and installing all dependencies.

Usage:
    python setup_environment.py
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_banner():
    """Print setup banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    FlowerCount-YOLO Environment Setup                       ║
║                                                                              ║
║    🌸 Advanced Flower Detection and Counting System                         ║
║    🔧 Automated Environment Configuration                                    ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_conda():
    """Check if conda is available"""
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Conda found: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Conda not found!")
        print("Please install Anaconda or Miniconda first:")
        print("   - Anaconda: https://www.anaconda.com/products/distribution")
        print("   - Miniconda: https://docs.conda.io/en/latest/miniconda.html")
        return False


def check_system():
    """Check system information"""
    print(f"🖥️  System: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Working Directory: {Path.cwd()}")


def create_conda_environment():
    """Create conda environment for FlowerCount-YOLO"""
    
    env_name = "flowercount-yolo"
    python_version = "3.9"
    
    print(f"\n🔨 Creating conda environment: {env_name}")
    print(f"🐍 Python version: {python_version}")
    
    # Check if environment already exists
    try:
        result = subprocess.run(['conda', 'env', 'list'], 
                              capture_output=True, text=True, check=True)
        if env_name in result.stdout:
            print(f"⚠️  Environment '{env_name}' already exists!")
            response = input("Do you want to remove and recreate it? (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                print(f"🗑️  Removing existing environment...")
                subprocess.run(['conda', 'env', 'remove', '-n', env_name, '-y'], 
                             check=True)
            else:
                print(f"✅ Using existing environment: {env_name}")
                return env_name
    except subprocess.CalledProcessError:
        pass
    
    # Create new environment
    print(f"🔨 Creating new environment...")
    try:
        subprocess.run([
            'conda', 'create', '-n', env_name, 
            f'python={python_version}', '-y'
        ], check=True)
        print(f"✅ Environment '{env_name}' created successfully!")
        return env_name
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create environment: {e}")
        return None


def install_pytorch():
    """Install PyTorch with appropriate CUDA support"""
    
    print(f"\n🔥 Installing PyTorch...")
    
    # Detect CUDA availability
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA detected: {torch.version.cuda}")
            cuda_version = torch.version.cuda
        else:
            print("⚠️  CUDA not available, installing CPU version")
            cuda_version = None
    except ImportError:
        # Check NVIDIA-SMI for CUDA
        try:
            result = subprocess.run(['nvidia-smi'], 
                                  capture_output=True, text=True, check=True)
            print("✅ NVIDIA GPU detected")
            cuda_version = "11.8"  # Default CUDA version
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  No NVIDIA GPU detected, installing CPU version")
            cuda_version = None
    
    # Install PyTorch
    if cuda_version:
        pytorch_cmd = [
            'conda', 'install', 'pytorch', 'torchvision', 'torchaudio', 
            'pytorch-cuda=11.8', '-c', 'pytorch', '-c', 'nvidia', '-y'
        ]
    else:
        pytorch_cmd = [
            'conda', 'install', 'pytorch', 'torchvision', 'torchaudio', 
            'cpuonly', '-c', 'pytorch', '-y'
        ]
    
    try:
        subprocess.run(pytorch_cmd, check=True)
        print("✅ PyTorch installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyTorch: {e}")
        return False


def install_dependencies():
    """Install project dependencies"""
    
    print(f"\n📦 Installing project dependencies...")
    
    # Install from requirements.txt
    requirements_file = Path("requirements.txt")
    if requirements_file.exists():
        try:
            subprocess.run(['pip', 'install', '-r', str(requirements_file)], check=True)
            print("✅ Dependencies installed from requirements.txt")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    else:
        print("⚠️  requirements.txt not found, installing essential packages...")
        
        essential_packages = [
            'ultralytics>=8.0.0',
            'opencv-python>=4.8.0',
            'matplotlib>=3.7.0',
            'seaborn>=0.12.0',
            'pandas>=2.0.0',
            'numpy>=1.24.0',
            'scipy>=1.11.0',
            'scikit-learn>=1.3.0',
            'tqdm>=4.65.0',
            'pyyaml>=6.0',
            'pillow>=10.0.0'
        ]
        
        for package in essential_packages:
            try:
                subprocess.run(['pip', 'install', package], check=True)
                print(f"✅ Installed: {package}")
            except subprocess.CalledProcessError:
                print(f"⚠️  Failed to install: {package}")
    
    return True


def install_optional_packages():
    """Install optional packages for advanced features"""
    
    print(f"\n🎯 Installing optional packages for advanced features...")
    
    optional_packages = [
        ('wandb', 'Experiment tracking'),
        ('tensorboard', 'TensorBoard logging'),
        ('grad-cam', 'GradCAM visualization'),
        ('transformers', 'CLIP model for auto-annotation'),
    ]
    
    for package, description in optional_packages:
        response = input(f"Install {package} for {description}? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            try:
                subprocess.run(['pip', 'install', package], check=True)
                print(f"✅ Installed: {package}")
            except subprocess.CalledProcessError:
                print(f"⚠️  Failed to install: {package}")


def create_activation_script():
    """Create environment activation script"""
    
    env_name = "flowercount-yolo"
    
    # Windows batch script
    if platform.system() == "Windows":
        script_content = f"""@echo off
echo 🌸 Activating FlowerCount-YOLO Environment...
call conda activate {env_name}
echo ✅ Environment activated!
echo 🚀 Ready to run FlowerCount-YOLO experiments!
echo.
echo Quick commands:
echo   python quick_start.py --mode demo
echo   python run_complete_experiment.py
cmd /k
"""
        with open("activate_env.bat", 'w') as f:
            f.write(script_content)
        print("✅ Created activation script: activate_env.bat")
    
    # Unix shell script
    else:
        script_content = f"""#!/bin/bash
echo "🌸 Activating FlowerCount-YOLO Environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate {env_name}
echo "✅ Environment activated!"
echo "🚀 Ready to run FlowerCount-YOLO experiments!"
echo ""
echo "Quick commands:"
echo "  python quick_start.py --mode demo"
echo "  python run_complete_experiment.py"
exec bash
"""
        with open("activate_env.sh", 'w') as f:
            f.write(script_content)
        os.chmod("activate_env.sh", 0o755)
        print("✅ Created activation script: activate_env.sh")


def verify_installation():
    """Verify the installation"""
    
    print(f"\n🔍 Verifying installation...")
    
    # Test imports
    test_imports = [
        ('torch', 'PyTorch'),
        ('torchvision', 'TorchVision'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('matplotlib', 'Matplotlib'),
        ('pandas', 'Pandas'),
        ('sklearn', 'Scikit-learn'),
        ('yaml', 'PyYAML'),
        ('PIL', 'Pillow'),
        ('ultralytics', 'Ultralytics')
    ]
    
    success_count = 0
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
            success_count += 1
        except ImportError:
            print(f"❌ {name}")
    
    print(f"\n📊 Installation Summary: {success_count}/{len(test_imports)} packages verified")
    
    if success_count >= len(test_imports) * 0.8:  # 80% success rate
        print("🎉 Installation verification successful!")
        return True
    else:
        print("⚠️  Some packages failed to install. Please check the errors above.")
        return False


def print_next_steps():
    """Print next steps for the user"""
    
    print(f"\n🎯 Next Steps:")
    print("=" * 50)
    
    if platform.system() == "Windows":
        print("1. 🔄 Activate environment:")
        print("   Double-click: activate_env.bat")
        print("   Or manually: conda activate flowercount-yolo")
    else:
        print("1. 🔄 Activate environment:")
        print("   Run: ./activate_env.sh")
        print("   Or manually: conda activate flowercount-yolo")
    
    print("\n2. 🎮 Run demo mode:")
    print("   python quick_start.py --mode demo")
    
    print("\n3. 🚀 Run complete experiment:")
    print("   python run_complete_experiment.py")
    
    print("\n4. 📊 Check your data:")
    print("   Make sure flower images are in the 'data/' directory")
    
    print("\n5. 📖 Read documentation:")
    print("   Check README_FlowerCount.md for detailed instructions")


def main():
    """Main setup function"""
    
    print_banner()
    
    # Check system
    print("🔍 Checking system...")
    check_system()
    
    # Check conda
    if not check_conda():
        return
    
    # Create environment
    env_name = create_conda_environment()
    if not env_name:
        return
    
    print(f"\n⚠️  IMPORTANT: The following steps will run in the '{env_name}' environment")
    print("Please activate the environment manually and run the installation:")
    print(f"conda activate {env_name}")
    print("python -c \"from setup_environment import install_pytorch, install_dependencies, install_optional_packages, verify_installation; install_pytorch(); install_dependencies(); verify_installation()\"")
    
    # Create activation script
    create_activation_script()
    
    # Print next steps
    print_next_steps()
    
    print(f"\n🎉 Environment setup completed!")
    print(f"🌸 Welcome to FlowerCount-YOLO!")


if __name__ == "__main__":
    main()
