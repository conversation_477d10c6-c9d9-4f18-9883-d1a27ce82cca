@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        FlowerCount-YOLO Auto Setup                          ║
echo ║                                                                              ║
echo ║    🌸 Advanced Flower Detection and Counting System                         ║
echo ║    🔧 Automated Environment Setup and Project Launch                        ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Conda not found!
    echo Please install Anaconda or Miniconda first:
    echo    - Anaconda: https://www.anaconda.com/products/distribution
    echo    - Miniconda: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo ✅ Conda found!

REM Check if FlowerCount-YOLO environment exists
conda env list | findstr "flowercount-yolo" >nul
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 🔨 FlowerCount-YOLO environment not found. Creating it...
    echo.
    
    REM Create environment
    echo 📦 Creating conda environment with Python 3.9...
    conda create -n flowercount-yolo python=3.9 -y
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Failed to create environment!
        pause
        exit /b 1
    )
    
    echo ✅ Environment created successfully!
    
    REM Activate environment and install packages
    echo.
    echo 🔥 Installing PyTorch and dependencies...
    call conda activate flowercount-yolo
    
    REM Install PyTorch (CPU version for compatibility)
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Failed to install PyTorch!
        pause
        exit /b 1
    )
    
    REM Install other dependencies
    pip install ultralytics>=8.0.0
    pip install opencv-python>=4.8.0
    pip install matplotlib>=3.7.0
    pip install seaborn>=0.12.0
    pip install pandas>=2.0.0
    pip install numpy>=1.24.0
    pip install scipy>=1.11.0
    pip install scikit-learn>=1.3.0
    pip install tqdm>=4.65.0
    pip install pyyaml>=6.0
    pip install pillow>=10.0.0
    
    echo ✅ Dependencies installed!
    
) else (
    echo ✅ FlowerCount-YOLO environment found!
    call conda activate flowercount-yolo
)

echo.
echo 🚀 Starting FlowerCount-YOLO Project...
echo.

REM Run the project starter
python start_project.py

echo.
echo 🌸 Thank you for using FlowerCount-YOLO!
pause
