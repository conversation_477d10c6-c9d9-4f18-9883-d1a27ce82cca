"""
Explainability and Visualization Module for FlowerCount-YOLO

This module provides comprehensive visualization and explainability tools:
1. Attention map visualization
2. GradCAM and variants
3. Feature map visualization
4. Model decision analysis
5. Density map visualization

Paper: "FlowerCount-YOLO: A Novel Multi-Scale Attention-Enhanced Framework 
       for Accurate Flower Detection and Counting"
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import os

try:
    from pytorch_grad_cam import GradCAM, GradCAMPlusPlus, ScoreCAM, LayerCAM
    from pytorch_grad_cam.utils.model_targets import ClassifierOutputTarget
    from pytorch_grad_cam.utils.image import show_cam_on_image
except ImportError:
    print("pytorch-grad-cam not available. Install with: pip install grad-cam")


class FlowerCountExplainer:
    """
    Comprehensive explainability system for FlowerCount-YOLO
    
    Provides multiple visualization techniques to understand model decisions:
    - Attention maps from CBAM modules
    - GradCAM variants for localization
    - Feature map visualizations
    - Density map analysis
    """
    
    def __init__(self, model, device='cuda'):
        self.model = model
        self.device = device
        self.model.eval()
        
        # Initialize GradCAM variants
        self.gradcam_methods = {}
        self._setup_gradcam_methods()
        
        # Storage for intermediate activations
        self.activations = {}
        self.gradients = {}
        
    def _setup_gradcam_methods(self):
        """Setup different GradCAM methods"""
        
        try:
            # Find target layers for GradCAM
            target_layers = self._find_target_layers()
            
            if target_layers:
                self.gradcam_methods = {
                    'GradCAM': GradCAM(model=self.model, target_layers=target_layers),
                    'GradCAM++': GradCAMPlusPlus(model=self.model, target_layers=target_layers),
                    'ScoreCAM': ScoreCAM(model=self.model, target_layers=target_layers),
                    'LayerCAM': LayerCAM(model=self.model, target_layers=target_layers)
                }
        except Exception as e:
            print(f"GradCAM setup failed: {e}")
            self.gradcam_methods = {}
    
    def _find_target_layers(self) -> List[nn.Module]:
        """Find suitable target layers for GradCAM"""
        
        target_layers = []
        
        # Look for convolutional layers in the model
        for name, module in self.model.named_modules():
            if isinstance(module, (nn.Conv2d, nn.BatchNorm2d)):
                # Focus on later layers for better localization
                if any(keyword in name.lower() for keyword in ['neck', 'head', 'detect']):
                    target_layers.append(module)
        
        # If no specific layers found, use the last few conv layers
        if not target_layers:
            conv_layers = []
            for module in self.model.modules():
                if isinstance(module, nn.Conv2d):
                    conv_layers.append(module)
            
            # Use last 3 conv layers
            target_layers = conv_layers[-3:] if len(conv_layers) >= 3 else conv_layers
        
        return target_layers
    
    def generate_attention_maps(self, 
                              image: torch.Tensor, 
                              save_dir: str = None) -> Dict[str, np.ndarray]:
        """
        Generate attention maps from CBAM modules
        
        Args:
            image: Input image tensor [1, C, H, W]
            save_dir: Directory to save visualizations
            
        Returns:
            Dictionary of attention maps
        """
        
        attention_maps = {}
        
        # Register hooks to capture attention weights
        hooks = []
        
        def get_attention_hook(name):
            def hook(module, input, output):
                if hasattr(module, 'channel_attention'):
                    # Capture channel attention
                    channel_att = module.channel_attention(input[0])
                    attention_maps[f'{name}_channel'] = channel_att.detach().cpu().numpy()
                
                if hasattr(module, 'spatial_attention'):
                    # Capture spatial attention
                    spatial_att = module.spatial_attention(input[0])
                    attention_maps[f'{name}_spatial'] = spatial_att.detach().cpu().numpy()
            
            return hook
        
        # Find CBAM modules and register hooks
        for name, module in self.model.named_modules():
            if 'attention' in name.lower() or 'cbam' in name.lower():
                hook = module.register_forward_hook(get_attention_hook(name))
                hooks.append(hook)
        
        # Forward pass to generate attention maps
        with torch.no_grad():
            _ = self.model(image)
        
        # Remove hooks
        for hook in hooks:
            hook.remove()
        
        # Visualize attention maps
        if save_dir and attention_maps:
            self._visualize_attention_maps(image, attention_maps, save_dir)
        
        return attention_maps
    
    def generate_gradcam_maps(self, 
                            image: torch.Tensor, 
                            target_class: int = None,
                            save_dir: str = None) -> Dict[str, np.ndarray]:
        """
        Generate GradCAM visualizations
        
        Args:
            image: Input image tensor [1, C, H, W]
            target_class: Target class for GradCAM (None for highest prediction)
            save_dir: Directory to save visualizations
            
        Returns:
            Dictionary of GradCAM maps
        """
        
        gradcam_maps = {}
        
        if not self.gradcam_methods:
            return gradcam_maps
        
        # Prepare input
        input_tensor = image.to(self.device)
        
        # Generate GradCAM for each method
        for method_name, gradcam in self.gradcam_methods.items():
            try:
                # Generate CAM
                targets = [ClassifierOutputTarget(target_class)] if target_class is not None else None
                cam = gradcam(input_tensor=input_tensor, targets=targets)
                
                gradcam_maps[method_name] = cam[0]  # Take first image in batch
                
            except Exception as e:
                print(f"Failed to generate {method_name}: {e}")
                continue
        
        # Visualize GradCAM maps
        if save_dir and gradcam_maps:
            self._visualize_gradcam_maps(image, gradcam_maps, save_dir)
        
        return gradcam_maps
    
    def generate_feature_maps(self, 
                            image: torch.Tensor, 
                            layer_names: List[str] = None,
                            save_dir: str = None) -> Dict[str, np.ndarray]:
        """
        Generate feature map visualizations
        
        Args:
            image: Input image tensor [1, C, H, W]
            layer_names: Specific layer names to visualize
            save_dir: Directory to save visualizations
            
        Returns:
            Dictionary of feature maps
        """
        
        feature_maps = {}
        hooks = []
        
        def get_feature_hook(name):
            def hook(module, input, output):
                feature_maps[name] = output.detach().cpu().numpy()
            return hook
        
        # Register hooks for specified layers or key layers
        target_layers = layer_names if layer_names else ['backbone', 'neck', 'head']
        
        for name, module in self.model.named_modules():
            if any(target in name.lower() for target in target_layers):
                if isinstance(module, (nn.Conv2d, nn.BatchNorm2d)):
                    hook = module.register_forward_hook(get_feature_hook(name))
                    hooks.append(hook)
        
        # Forward pass
        with torch.no_grad():
            _ = self.model(image)
        
        # Remove hooks
        for hook in hooks:
            hook.remove()
        
        # Visualize feature maps
        if save_dir and feature_maps:
            self._visualize_feature_maps(feature_maps, save_dir)
        
        return feature_maps
    
    def analyze_density_maps(self, 
                           image: torch.Tensor, 
                           ground_truth_density: torch.Tensor = None,
                           save_dir: str = None) -> Dict[str, Any]:
        """
        Analyze density map predictions
        
        Args:
            image: Input image tensor [1, C, H, W]
            ground_truth_density: Ground truth density map
            save_dir: Directory to save visualizations
            
        Returns:
            Dictionary of density analysis results
        """
        
        analysis_results = {}
        
        # Get model predictions
        with torch.no_grad():
            predictions = self.model(image)
        
        if 'density_map' not in predictions:
            return analysis_results
        
        predicted_density = predictions['density_map']
        
        # Convert to numpy
        pred_density_np = predicted_density.squeeze().cpu().numpy()
        
        # Calculate statistics
        analysis_results['predicted_count'] = np.sum(pred_density_np)
        analysis_results['max_density'] = np.max(pred_density_np)
        analysis_results['mean_density'] = np.mean(pred_density_np)
        analysis_results['density_std'] = np.std(pred_density_np)
        
        # Compare with ground truth if available
        if ground_truth_density is not None:
            gt_density_np = ground_truth_density.squeeze().cpu().numpy()
            
            analysis_results['gt_count'] = np.sum(gt_density_np)
            analysis_results['count_error'] = analysis_results['predicted_count'] - analysis_results['gt_count']
            analysis_results['density_mse'] = np.mean((pred_density_np - gt_density_np) ** 2)
            analysis_results['density_mae'] = np.mean(np.abs(pred_density_np - gt_density_np))
        
        # Visualize density maps
        if save_dir:
            self._visualize_density_maps(
                image, predicted_density, ground_truth_density, analysis_results, save_dir
            )
        
        return analysis_results
    
    def _visualize_attention_maps(self, 
                                image: torch.Tensor, 
                                attention_maps: Dict[str, np.ndarray], 
                                save_dir: str):
        """Visualize attention maps"""
        
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Convert image to numpy for visualization
        img_np = image.squeeze().cpu().numpy().transpose(1, 2, 0)
        img_np = (img_np - img_np.min()) / (img_np.max() - img_np.min())
        
        # Create subplots for all attention maps
        n_maps = len(attention_maps)
        if n_maps == 0:
            return
        
        cols = min(4, n_maps)
        rows = (n_maps + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(4*cols, 4*rows))
        if rows == 1:
            axes = [axes] if cols == 1 else axes
        else:
            axes = axes.flatten()
        
        for i, (name, att_map) in enumerate(attention_maps.items()):
            if i >= len(axes):
                break
            
            # Process attention map
            if len(att_map.shape) == 4:  # [B, C, H, W]
                att_map = att_map[0].mean(axis=0)  # Average across channels
            elif len(att_map.shape) == 3:  # [C, H, W]
                att_map = att_map.mean(axis=0)  # Average across channels
            
            # Resize to match image size
            att_map_resized = cv2.resize(att_map, (img_np.shape[1], img_np.shape[0]))
            
            # Normalize
            att_map_resized = (att_map_resized - att_map_resized.min()) / \
                            (att_map_resized.max() - att_map_resized.min() + 1e-8)
            
            # Create overlay
            overlay = show_cam_on_image(img_np, att_map_resized, use_rgb=True)
            
            axes[i].imshow(overlay)
            axes[i].set_title(name)
            axes[i].axis('off')
        
        # Hide unused subplots
        for i in range(len(attention_maps), len(axes)):
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path / 'attention_maps.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _visualize_gradcam_maps(self, 
                              image: torch.Tensor, 
                              gradcam_maps: Dict[str, np.ndarray], 
                              save_dir: str):
        """Visualize GradCAM maps"""
        
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Convert image to numpy
        img_np = image.squeeze().cpu().numpy().transpose(1, 2, 0)
        img_np = (img_np - img_np.min()) / (img_np.max() - img_np.min())
        
        # Create subplots
        n_methods = len(gradcam_maps)
        fig, axes = plt.subplots(1, n_methods + 1, figsize=(4*(n_methods + 1), 4))
        
        if n_methods == 0:
            return
        
        # Show original image
        axes[0].imshow(img_np)
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # Show GradCAM results
        for i, (method_name, cam) in enumerate(gradcam_maps.items()):
            overlay = show_cam_on_image(img_np, cam, use_rgb=True)
            axes[i + 1].imshow(overlay)
            axes[i + 1].set_title(method_name)
            axes[i + 1].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path / 'gradcam_maps.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _visualize_feature_maps(self, 
                              feature_maps: Dict[str, np.ndarray], 
                              save_dir: str):
        """Visualize feature maps"""
        
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        for layer_name, features in feature_maps.items():
            # Take first sample in batch
            if len(features.shape) == 4:
                features = features[0]
            
            # Select subset of channels to visualize
            n_channels = min(16, features.shape[0])
            selected_features = features[:n_channels]
            
            # Create subplot grid
            cols = 4
            rows = (n_channels + cols - 1) // cols
            
            fig, axes = plt.subplots(rows, cols, figsize=(12, 3*rows))
            if rows == 1:
                axes = [axes] if cols == 1 else axes
            else:
                axes = axes.flatten()
            
            for i in range(n_channels):
                feature_map = selected_features[i]
                
                # Normalize
                feature_map = (feature_map - feature_map.min()) / \
                            (feature_map.max() - feature_map.min() + 1e-8)
                
                axes[i].imshow(feature_map, cmap='viridis')
                axes[i].set_title(f'Channel {i}')
                axes[i].axis('off')
            
            # Hide unused subplots
            for i in range(n_channels, len(axes)):
                axes[i].axis('off')
            
            plt.suptitle(f'Feature Maps: {layer_name}')
            plt.tight_layout()
            
            # Save with safe filename
            safe_name = layer_name.replace('/', '_').replace('.', '_')
            plt.savefig(save_path / f'feature_maps_{safe_name}.png', 
                       dpi=300, bbox_inches='tight')
            plt.close()
    
    def _visualize_density_maps(self, 
                              image: torch.Tensor, 
                              predicted_density: torch.Tensor, 
                              ground_truth_density: torch.Tensor = None,
                              analysis_results: Dict = None,
                              save_dir: str = None):
        """Visualize density maps"""
        
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Convert to numpy
        img_np = image.squeeze().cpu().numpy().transpose(1, 2, 0)
        img_np = (img_np - img_np.min()) / (img_np.max() - img_np.min())
        
        pred_density_np = predicted_density.squeeze().cpu().numpy()
        
        # Create subplots
        n_plots = 3 if ground_truth_density is not None else 2
        fig, axes = plt.subplots(1, n_plots, figsize=(6*n_plots, 5))
        
        # Original image
        axes[0].imshow(img_np)
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # Predicted density
        im1 = axes[1].imshow(pred_density_np, cmap='hot', alpha=0.8)
        axes[1].set_title(f'Predicted Density\nCount: {analysis_results.get("predicted_count", 0):.1f}')
        plt.colorbar(im1, ax=axes[1])
        
        # Ground truth density (if available)
        if ground_truth_density is not None:
            gt_density_np = ground_truth_density.squeeze().cpu().numpy()
            im2 = axes[2].imshow(gt_density_np, cmap='hot', alpha=0.8)
            axes[2].set_title(f'Ground Truth Density\nCount: {analysis_results.get("gt_count", 0):.1f}')
            plt.colorbar(im2, ax=axes[2])
        
        plt.tight_layout()
        plt.savefig(save_path / 'density_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_comprehensive_analysis(self, 
                                      image: torch.Tensor, 
                                      ground_truth: Dict = None,
                                      save_dir: str = None) -> Dict[str, Any]:
        """
        Generate comprehensive explainability analysis
        
        Args:
            image: Input image tensor
            ground_truth: Ground truth annotations
            save_dir: Directory to save all visualizations
            
        Returns:
            Complete analysis results
        """
        
        if save_dir:
            save_path = Path(save_dir)
            save_path.mkdir(parents=True, exist_ok=True)
        
        analysis = {}
        
        # Generate attention maps
        print("Generating attention maps...")
        attention_maps = self.generate_attention_maps(image, save_dir)
        analysis['attention_maps'] = attention_maps
        
        # Generate GradCAM maps
        print("Generating GradCAM maps...")
        gradcam_maps = self.generate_gradcam_maps(image, save_dir=save_dir)
        analysis['gradcam_maps'] = gradcam_maps
        
        # Generate feature maps
        print("Generating feature maps...")
        feature_maps = self.generate_feature_maps(image, save_dir=save_dir)
        analysis['feature_maps'] = feature_maps
        
        # Analyze density maps
        print("Analyzing density maps...")
        gt_density = ground_truth.get('density_map') if ground_truth else None
        density_analysis = self.analyze_density_maps(image, gt_density, save_dir)
        analysis['density_analysis'] = density_analysis
        
        # Save analysis summary
        if save_dir:
            summary_path = save_path / 'analysis_summary.json'
            import json
            
            # Convert numpy arrays to lists for JSON serialization
            json_analysis = {}
            for key, value in analysis.items():
                if key == 'density_analysis':
                    json_analysis[key] = {k: float(v) if isinstance(v, (np.float32, np.float64)) else v 
                                        for k, v in value.items()}
                else:
                    json_analysis[key] = f"Generated {len(value)} maps" if isinstance(value, dict) else str(value)
            
            with open(summary_path, 'w') as f:
                json.dump(json_analysis, f, indent=2)
        
        print("Comprehensive analysis completed!")
        return analysis


def analyze_model_predictions(model, 
                            image_path: str, 
                            ground_truth: Dict = None,
                            save_dir: str = None):
    """
    Analyze model predictions with comprehensive explainability
    
    Args:
        model: Trained FlowerCount-YOLO model
        image_path: Path to input image
        ground_truth: Ground truth annotations
        save_dir: Directory to save analysis results
    """
    
    # Load and preprocess image
    image = cv2.imread(image_path)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Convert to tensor
    image_tensor = torch.from_numpy(image_rgb).permute(2, 0, 1).float() / 255.0
    image_tensor = image_tensor.unsqueeze(0)  # Add batch dimension
    
    # Initialize explainer
    explainer = FlowerCountExplainer(model)
    
    # Generate comprehensive analysis
    analysis = explainer.generate_comprehensive_analysis(
        image_tensor, ground_truth, save_dir
    )
    
    return analysis


if __name__ == "__main__":
    # Example usage
    print("FlowerCount-YOLO Explainability Module")
    print("This module provides comprehensive model analysis and visualization tools.")
    
    # Create dummy model for testing
    class DummyModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.conv = nn.Conv2d(3, 64, 3, padding=1)
            self.pool = nn.AdaptiveAvgPool2d(1)
            self.fc = nn.Linear(64, 1)
        
        def forward(self, x):
            x = self.conv(x)
            x = self.pool(x)
            x = x.view(x.size(0), -1)
            count = self.fc(x)
            
            return {
                'predicted_count': count,
                'density_map': torch.randn(x.size(0), 1, 32, 32)
            }
    
    # Test with dummy data
    model = DummyModel()
    dummy_image = torch.randn(1, 3, 256, 256)
    
    explainer = FlowerCountExplainer(model, device='cpu')
    
    # Test attention map generation
    attention_maps = explainer.generate_attention_maps(dummy_image)
    print(f"Generated {len(attention_maps)} attention maps")
    
    # Test density analysis
    density_analysis = explainer.analyze_density_maps(dummy_image)
    print(f"Density analysis: {density_analysis}")
    
    print("Explainability module test completed!")
