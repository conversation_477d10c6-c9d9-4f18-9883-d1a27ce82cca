#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlowerCount-YOLO 基础训练脚本
"""

import os
import sys
from pathlib import Path

def main():
    print("🌸 FlowerCount-YOLO 基础训练开始")
    print("=" * 50)
    
    try:
        # 检查基础环境
        print("🔍 检查Python环境...")
        print(f"   Python版本: {sys.version}")
        print(f"   工作目录: {os.getcwd()}")
        
        # 检查数据集
        print("\n📊 检查数据集...")
        dataset_dir = Path("dataset")
        if not dataset_dir.exists():
            print("❌ dataset目录不存在")
            return False
        
        train_images = list((dataset_dir / "train" / "images").glob("*.jpg"))
        val_images = list((dataset_dir / "val" / "images").glob("*.jpg"))
        
        print(f"   训练图像: {len(train_images)}")
        print(f"   验证图像: {len(val_images)}")
        
        if len(train_images) == 0:
            print("❌ 没有找到训练图像")
            return False
        
        # 导入ultralytics
        print("\n📦 导入ultralytics...")
        try:
            from ultralytics import YOLO
            print("   ✅ ultralytics导入成功")
        except ImportError as e:
            print(f"   ❌ ultralytics导入失败: {e}")
            print("   请运行: pip install ultralytics")
            return False
        
        # 创建模型
        print("\n🎯 创建YOLO模型...")
        try:
            model = YOLO('yolov8n.pt')
            print("   ✅ 模型创建成功")
        except Exception as e:
            print(f"   ❌ 模型创建失败: {e}")
            return False
        
        # 检查数据集配置文件
        dataset_yaml = dataset_dir / "dataset.yaml"
        if not dataset_yaml.exists():
            print("❌ dataset.yaml不存在，创建基础配置...")
            # 创建基础配置
            config_content = f"""path: {dataset_dir.absolute()}
train: train/images
val: val/images
test: test/images

nc: 1
names: ['flower']
"""
            with open(dataset_yaml, 'w', encoding='utf-8') as f:
                f.write(config_content)
            print(f"   ✅ 创建配置文件: {dataset_yaml}")
        
        # 开始训练
        print("\n🚀 开始训练...")
        print("   参数:")
        print("   - 数据集:", str(dataset_yaml))
        print("   - 轮数: 30")
        print("   - 图像尺寸: 640")
        print("   - 批次大小: 4")
        
        try:
            results = model.train(
                data=str(dataset_yaml),
                epochs=30,
                imgsz=640,
                batch=4,
                device='auto',
                workers=2,
                patience=10,
                save=True,
                val=True,
                plots=True,
                verbose=True,
                project='runs/train',
                name='flowercount_basic',
                exist_ok=True
            )
            
            print("\n🎉 训练完成！")
            print(f"   模型保存目录: {results.save_dir}")
            
            # 保存训练信息
            import json
            train_info = {
                'model_path': str(results.save_dir / 'weights' / 'best.pt'),
                'results_dir': str(results.save_dir),
                'train_images': len(train_images),
                'val_images': len(val_images)
            }
            
            with open('training_basic_info.json', 'w', encoding='utf-8') as f:
                json.dump(train_info, f, indent=2, ensure_ascii=False)
            
            print("   ✅ 训练信息已保存")
            return True
            
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 程序执行成功！")
    else:
        print("\n❌ 程序执行失败！")
