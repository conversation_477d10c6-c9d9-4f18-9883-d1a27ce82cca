# FlowerCount-YOLO 发表级配置文件
# 专门针对高密度花朵检测与计数优化

# 数据集配置
path: ./dataset  # 数据集根目录
train: train/images  # 训练集图像路径
val: val/images      # 验证集图像路径
test: test/images    # 测试集图像路径

# 类别配置
nc: 1  # 类别数量
names: ['flower']  # 类别名称

# 模型配置
model:
  # 基础架构
  backbone: 'yolov8n'  # 骨干网络
  neck: 'FPN+PAN'      # 颈部网络
  head: 'YOLOv8Head'   # 检测头
  
  # 注意力机制
  attention:
    type: 'CBAM'       # 注意力类型
    reduction: 16      # 通道注意力缩减比例
    kernel_size: 7     # 空间注意力核大小
  
  # 多尺度特征融合
  feature_fusion:
    type: 'FPN+PAN'    # 特征融合类型
    channels: [256, 512, 1024]  # 特征通道数
  
  # 密度估计分支
  density_estimation:
    enabled: true      # 是否启用密度估计
    hidden_channels: 256  # 隐藏层通道数
    loss_weight: 0.5   # 密度损失权重

# 训练配置
train:
  # 基础参数
  epochs: 100          # 训练轮数
  batch_size: 4        # 批次大小（适应高分辨率）
  imgsz: 1280         # 输入图像尺寸
  device: 'auto'       # 设备选择
  workers: 4           # 数据加载线程数
  
  # 优化器配置
  optimizer: 'AdamW'   # 优化器类型
  lr0: 0.001          # 初始学习率
  lrf: 0.01           # 最终学习率比例
  momentum: 0.937      # 动量
  weight_decay: 0.0005 # 权重衰减
  
  # 学习率调度
  warmup_epochs: 3     # 预热轮数
  warmup_momentum: 0.8 # 预热动量
  warmup_bias_lr: 0.1  # 预热偏置学习率
  
  # 损失函数权重
  box: 7.5            # 边界框损失权重
  cls: 0.5            # 分类损失权重
  dfl: 1.5            # 分布焦点损失权重
  
  # 正则化
  label_smoothing: 0.0 # 标签平滑
  dropout: 0.0         # Dropout比例
  
  # 训练策略
  patience: 20         # 早停耐心值
  save_period: 10      # 模型保存周期
  val: true           # 是否验证
  plots: true         # 是否生成图表
  save_json: true     # 是否保存JSON结果
  
  # 混合精度训练
  amp: true           # 自动混合精度
  
  # 多尺度训练
  multi_scale: true   # 多尺度训练
  rect: false         # 矩形训练
  
  # 数据增强
  augment: true       # 是否数据增强
  auto_augment: 'randaugment'  # 自动增强策略
  erasing: 0.4        # 随机擦除概率
  crop_fraction: 1.0  # 裁剪比例
  copy_paste: 0.0     # 复制粘贴增强
  
  # Mosaic增强
  mosaic: 1.0         # Mosaic增强概率
  mixup: 0.0          # Mixup增强概率
  close_mosaic: 10    # 关闭Mosaic的轮数
  
  # 缓存配置
  cache: false        # 是否缓存图像
  
  # 冻结层配置
  freeze: null        # 冻结层数（null表示不冻结）

# 验证配置
val:
  batch_size: 1       # 验证批次大小
  imgsz: 1280        # 验证图像尺寸
  conf: 0.001        # 置信度阈值
  iou: 0.6           # IoU阈值
  max_det: 1000      # 最大检测数（适应高密度场景）
  half: false        # 是否使用半精度
  dnn: false         # 是否使用DNN后端
  plots: true        # 是否生成验证图表
  save_json: true    # 是否保存验证JSON
  save_hybrid: false # 是否保存混合标签
  save_conf: false   # 是否保存置信度
  save_txt: false    # 是否保存TXT结果
  verbose: true      # 详细输出

# 测试配置
test:
  batch_size: 1      # 测试批次大小
  imgsz: 1280       # 测试图像尺寸
  conf: 0.25        # 置信度阈值
  iou: 0.45         # IoU阈值
  max_det: 1000     # 最大检测数
  agnostic_nms: false  # 类别无关NMS
  augment: false    # 测试时增强
  visualize: false  # 可视化特征
  update: false     # 更新所有模型
  project: 'runs/detect'  # 项目目录
  name: 'exp'       # 实验名称
  exist_ok: false   # 覆盖现有项目
  line_thickness: 3 # 边界框线条粗细
  hide_labels: false   # 隐藏标签
  hide_conf: false     # 隐藏置信度
  half: false       # 半精度推理
  dnn: false        # DNN后端

# 导出配置
export:
  format: 'onnx'    # 导出格式
  imgsz: 1280      # 导出图像尺寸
  keras: false     # Keras格式
  optimize: false  # TorchScript优化
  int8: false      # INT8量化
  dynamic: false   # 动态轴
  simplify: false  # ONNX简化
  opset: null      # ONNX opset版本
  workspace: 4     # TensorRT工作空间大小(GB)
  nms: false       # 添加NMS到ONNX

# 高密度花朵特定配置
flower_specific:
  # 检测参数优化
  detection:
    min_flower_size: 200      # 最小花朵面积
    max_flower_ratio: 0.05    # 最大花朵面积比例
    confidence_threshold: 0.4  # 置信度阈值
    nms_threshold: 0.3        # NMS阈值（较低以保留更多检测）
    
  # 颜色范围配置
  color_ranges:
    red: [[0, 30, 30], [15, 255, 255], [165, 30, 30], [180, 255, 255]]
    pink: [[135, 20, 40], [175, 255, 255]]
    yellow: [[12, 30, 40], [40, 255, 255]]
    orange: [[5, 30, 40], [30, 255, 255]]
    purple: [[115, 20, 40], [145, 255, 255]]
    white: [[0, 0, 120], [180, 50, 255]]
    blue: [[95, 30, 40], [125, 255, 255]]
  
  # 多尺度检测
  scales: [1.0, 0.8, 0.6, 0.4]  # 检测尺度
  
  # 形态学操作
  morphology:
    kernel_size_base: 7        # 基础核大小
    operations: ['close', 'open']  # 形态学操作序列
  
  # 质量过滤
  quality_filter:
    min_compactness: 0.15      # 最小紧凑度
    min_perimeter: 0           # 最小周长
    aspect_ratio_range: [0.3, 3.0]  # 长宽比范围

# 实验配置
experiment:
  name: 'flowercount_yolo_publication'  # 实验名称
  description: 'FlowerCount-YOLO for high-density flower detection and counting'
  version: '1.0.0'
  author: 'FlowerCount-YOLO Team'
  
  # 日志配置
  logging:
    level: 'INFO'
    save_dir: 'logs'
    tensorboard: true
    wandb: false
  
  # 检查点配置
  checkpoint:
    save_best: true
    save_last: true
    save_period: 10
    resume: false
  
  # 评估指标
  metrics:
    primary: 'mAP50'
    secondary: ['mAP50-95', 'precision', 'recall', 'f1']
    counting_metrics: ['MAE', 'RMSE', 'MAPE']

# 硬件配置
hardware:
  # GPU配置
  gpu:
    enabled: true
    device_ids: [0]  # GPU设备ID
    memory_fraction: 0.9  # GPU内存使用比例
  
  # CPU配置
  cpu:
    num_workers: 4   # CPU工作线程数
    pin_memory: true # 固定内存
  
  # 内存配置
  memory:
    cache_images: false  # 缓存图像到内存
    prefetch_factor: 2   # 预取因子
