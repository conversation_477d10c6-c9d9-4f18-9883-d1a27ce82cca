# FlowerCount-YOLO 快速安装依赖列表
# 使用国内镜像源: pip install -r requirements_fast.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 注意: PyTorch需要单独安装CUDA版本
# pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 核心深度学习框架
ultralytics>=8.0.0
torchmetrics>=1.0.0

# 计算机视觉
opencv-python>=4.8.0
opencv-contrib-python>=4.8.0
albumentations>=1.3.0
scikit-image>=0.21.0
pillow>=10.0.0

# 科学计算
numpy>=1.24.0
scipy>=1.11.0
scikit-learn>=1.3.0
pandas>=2.0.0

# 可视化
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# 实验跟踪
wandb>=0.15.0
tensorboard>=2.13.0
mlflow>=2.5.0

# 基础模型和Transformers
transformers>=4.30.0
timm>=0.9.0

# 可解释性
grad-cam>=1.4.0

# 评估
pycocotools>=2.0.6

# 工具
tqdm>=4.65.0
pyyaml>=6.0
rich>=13.0.0
hydra-core>=1.3.0
requests>=2.31.0

# 统计分析
statsmodels>=0.14.0

# 模型优化
onnx>=1.14.0
onnxruntime>=1.15.0
