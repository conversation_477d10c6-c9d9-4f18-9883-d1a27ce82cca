#!/usr/bin/env python3
"""
FlowerCount-YOLO Project Starter

This script provides a simple way to start the FlowerCount-YOLO project
with automatic environment detection and setup.

Usage:
    python start_project.py
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
import logging


def setup_logging():
    """Setup logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('project_startup.log')
        ]
    )
    return logging.getLogger('ProjectStarter')


def print_banner():
    """Print project banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                        FlowerCount-YOLO Project                             ║
║                                                                              ║
║    🌸 Advanced Flower Detection and Counting System                         ║
║    🚀 Starting Your Research Journey                                        ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_environment():
    """Check if we're in the correct environment"""
    
    logger = setup_logging()
    
    print("🔍 Checking environment...")
    
    # Check if we're in conda environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✅ Conda environment: {conda_env}")
        if 'flowercount' in conda_env.lower() or 'flower' in conda_env.lower():
            print("✅ Looks like FlowerCount-YOLO environment!")
        else:
            print("⚠️  Not in FlowerCount-YOLO environment")
    else:
        print("⚠️  Not in conda environment")
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor} (requires 3.8+)")
        return False
    
    # Check key packages
    required_packages = ['torch', 'cv2', 'numpy', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Please run the environment setup first:")
        print("python setup_environment.py")
        return False
    
    return True


def check_data():
    """Check if data directory exists and has images"""
    
    print("\n📁 Checking data directory...")
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ Data directory not found!")
        print("Creating data directory...")
        data_dir.mkdir()
        print("✅ Data directory created")
        print("📝 Please add your flower images to the 'data/' directory")
        return False
    
    # Check for images
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(list(data_dir.glob(f'*{ext}')))
        image_files.extend(list(data_dir.glob(f'*{ext.upper()}')))
    
    if image_files:
        print(f"✅ Found {len(image_files)} images in data directory")
        return True
    else:
        print("⚠️  No images found in data directory")
        print("📝 Please add your flower images to the 'data/' directory")
        return False


def check_project_structure():
    """Check if project structure is correct"""
    
    print("\n🏗️  Checking project structure...")
    
    required_files = [
        'quick_start.py',
        'main_experiment.py',
        'requirements.txt',
        'configs/experiment_config.yaml'
    ]
    
    required_dirs = [
        'src',
        'src/models',
        'src/data',
        'src/training',
        'src/evaluation',
        'src/visualization'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
        else:
            print(f"✅ {dir_path}/")
    
    if missing_files or missing_dirs:
        print(f"\n❌ Missing files: {missing_files}")
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    
    print("✅ Project structure looks good!")
    return True


def show_menu():
    """Show main menu"""
    
    print("\n🎮 What would you like to do?")
    print("=" * 40)
    print("1. 🎯 Run Demo Mode (Recommended for first time)")
    print("2. 🚀 Start Quick Training (50 epochs)")
    print("3. 🔬 Run Complete Experiment (Full pipeline)")
    print("4. 📊 Generate Visualizations")
    print("5. 📏 Evaluate Model")
    print("6. 🛠️  Setup Environment")
    print("7. ❓ Help & Documentation")
    print("8. 🚪 Exit")
    print("=" * 40)
    
    while True:
        try:
            choice = input("Enter your choice (1-8): ").strip()
            if choice in ['1', '2', '3', '4', '5', '6', '7', '8']:
                return int(choice)
            else:
                print("Please enter a number between 1 and 8")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)


def run_demo():
    """Run demo mode"""
    print("\n🎯 Starting Demo Mode...")
    try:
        subprocess.run([sys.executable, 'quick_start.py', '--mode', 'demo'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Demo failed: {e}")
    except FileNotFoundError:
        print("❌ quick_start.py not found!")


def run_training():
    """Run quick training"""
    print("\n🚀 Starting Quick Training...")
    try:
        subprocess.run([sys.executable, 'quick_start.py', '--mode', 'train'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Training failed: {e}")
    except FileNotFoundError:
        print("❌ quick_start.py not found!")


def run_complete_experiment():
    """Run complete experiment"""
    print("\n🔬 Starting Complete Experiment...")
    print("⚠️  This will take several hours to complete!")
    
    response = input("Are you sure you want to continue? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        try:
            subprocess.run([sys.executable, 'run_complete_experiment.py'], check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ Experiment failed: {e}")
        except FileNotFoundError:
            print("❌ run_complete_experiment.py not found!")
    else:
        print("🛑 Experiment cancelled")


def run_visualization():
    """Run visualization generation"""
    print("\n📊 Generating Visualizations...")
    try:
        subprocess.run([sys.executable, 'quick_start.py', '--mode', 'viz'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Visualization failed: {e}")
    except FileNotFoundError:
        print("❌ quick_start.py not found!")


def run_evaluation():
    """Run model evaluation"""
    print("\n📏 Starting Model Evaluation...")
    try:
        subprocess.run([sys.executable, 'quick_start.py', '--mode', 'eval'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Evaluation failed: {e}")
    except FileNotFoundError:
        print("❌ quick_start.py not found!")


def setup_environment():
    """Run environment setup"""
    print("\n🛠️  Starting Environment Setup...")
    try:
        subprocess.run([sys.executable, 'setup_environment.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Setup failed: {e}")
    except FileNotFoundError:
        print("❌ setup_environment.py not found!")


def show_help():
    """Show help and documentation"""
    print("\n❓ FlowerCount-YOLO Help")
    print("=" * 30)
    print("\n📖 Documentation:")
    print("  • README_FlowerCount.md - Complete project documentation")
    print("  • PROJECT_SUMMARY.md - Project overview and features")
    print("  • configs/experiment_config.yaml - Configuration settings")
    
    print("\n🚀 Quick Start:")
    print("  1. Ensure your flower images are in the 'data/' directory")
    print("  2. Run demo mode to see the system in action")
    print("  3. Try quick training for a fast experiment")
    print("  4. Run complete experiment for full research pipeline")
    
    print("\n🔧 Troubleshooting:")
    print("  • If packages are missing, run environment setup")
    print("  • Check project_startup.log for detailed logs")
    print("  • Ensure you're in the correct conda environment")
    
    print("\n📞 Support:")
    print("  • Check the documentation files")
    print("  • Review the code comments")
    print("  • Examine the example configurations")


def main():
    """Main function"""
    
    print_banner()
    
    # Check environment
    if not check_environment():
        print("\n🛠️  Environment issues detected!")
        response = input("Would you like to run environment setup? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            setup_environment()
            return
        else:
            print("Please fix environment issues and try again.")
            return
    
    # Check project structure
    if not check_project_structure():
        print("\n❌ Project structure issues detected!")
        print("Please ensure all required files are present.")
        return
    
    # Check data
    has_data = check_data()
    
    # Main loop
    while True:
        choice = show_menu()
        
        if choice == 1:
            run_demo()
        elif choice == 2:
            if has_data:
                run_training()
            else:
                print("⚠️  Please add images to data/ directory first")
        elif choice == 3:
            if has_data:
                run_complete_experiment()
            else:
                print("⚠️  Please add images to data/ directory first")
        elif choice == 4:
            run_visualization()
        elif choice == 5:
            run_evaluation()
        elif choice == 6:
            setup_environment()
        elif choice == 7:
            show_help()
        elif choice == 8:
            print("\n👋 Thank you for using FlowerCount-YOLO!")
            print("🌸 Happy flower counting!")
            break
        
        # Ask if user wants to continue
        print("\n" + "="*50)
        response = input("Press Enter to continue or 'q' to quit: ").strip().lower()
        if response == 'q':
            print("\n👋 Goodbye!")
            break


if __name__ == "__main__":
    main()
