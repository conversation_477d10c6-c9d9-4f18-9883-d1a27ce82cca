#!/usr/bin/env python3
"""
FlowerCount-YOLO 基础训练脚本

使用Ultralytics YOLO进行基础的花朵检测训练
"""

import os
import sys
import json
import yaml
from pathlib import Path
from datetime import datetime
from ultralytics import YOLO
import torch


def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    FlowerCount-YOLO 基础训练                                ║
║                                                                              ║
║    🌸 使用Ultralytics YOLO进行花朵检测训练                                  ║
║    💻 CPU/GPU自适应训练                                                      ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_environment():
    """检查训练环境"""
    print("🔍 训练环境检查:")
    print("=" * 20)
    
    # 基本信息
    env_name = os.environ.get('CONDA_DEFAULT_ENV', 'base')
    print(f"   📦 Conda环境: {env_name}")
    print(f"   🐍 Python: {sys.version.split()[0]}")
    
    # PyTorch信息
    print(f"   🔥 PyTorch: {torch.__version__}")
    cuda_available = torch.cuda.is_available()
    print(f"   🎮 CUDA可用: {cuda_available}")
    
    if cuda_available:
        print(f"   💾 GPU: {torch.cuda.get_device_name(0)}")
        print(f"   📊 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        device = 'cuda'
    else:
        print(f"   💻 使用CPU训练")
        device = 'cpu'
    
    return device


def prepare_dataset():
    """准备数据集"""
    print("\n📁 准备数据集:")
    print("=" * 15)
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("   ❌ data目录不存在")
        return None
    
    # 检查图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(list(data_dir.glob(f'*{ext}')))
        image_files.extend(list(data_dir.glob(f'*{ext.upper()}')))
    
    if not image_files:
        print("   ❌ 未找到图像文件")
        return None
    
    print(f"   ✅ 找到 {len(image_files)} 张图像")
    
    # 创建YOLO格式的数据集配置
    dataset_config = {
        'path': str(data_dir.absolute()),
        'train': '.',  # 训练图像目录（当前data目录）
        'val': '.',    # 验证图像目录（当前data目录）
        'names': {
            0: 'flower'  # 类别名称
        },
        'nc': 1  # 类别数量
    }
    
    # 保存数据集配置
    config_file = Path("dataset.yaml")
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(dataset_config, f, default_flow_style=False)
    
    print(f"   ✅ 数据集配置: {config_file}")
    return config_file


def create_dummy_annotations():
    """创建虚拟标注文件用于演示"""
    print("\n🏷️  创建演示标注:")
    print("=" * 20)
    
    data_dir = Path("data")
    labels_dir = data_dir / "labels"
    labels_dir.mkdir(exist_ok=True)
    
    # 获取图像文件
    image_files = list(data_dir.glob('*.jpg')) + list(data_dir.glob('*.png'))
    
    if not image_files:
        print("   ❌ 没有图像文件")
        return False
    
    # 为前10张图像创建虚拟标注
    import random
    created_labels = 0
    
    for img_file in image_files[:10]:  # 只处理前10张
        label_file = labels_dir / f"{img_file.stem}.txt"
        
        # 创建1-3个虚拟边界框
        num_boxes = random.randint(1, 3)
        annotations = []
        
        for _ in range(num_boxes):
            # YOLO格式: class_id center_x center_y width height (归一化坐标)
            center_x = random.uniform(0.2, 0.8)
            center_y = random.uniform(0.2, 0.8)
            width = random.uniform(0.1, 0.3)
            height = random.uniform(0.1, 0.3)
            
            annotations.append(f"0 {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}")
        
        # 保存标注文件
        with open(label_file, 'w') as f:
            f.write('\n'.join(annotations))
        
        created_labels += 1
    
    print(f"   ✅ 创建了 {created_labels} 个标注文件")
    print(f"   📁 标注目录: {labels_dir}")
    
    return True


def train_yolo_model(dataset_config, device):
    """训练YOLO模型"""
    print("\n🚀 开始YOLO训练:")
    print("=" * 20)
    
    try:
        # 加载预训练模型
        print("   📥 加载YOLOv8n预训练模型...")
        model = YOLO('yolov8n.pt')  # 使用nano版本，适合CPU训练
        
        print(f"   🎯 设备: {device}")
        
        # 训练参数
        train_args = {
            'data': str(dataset_config),
            'epochs': 5,  # 少量epoch用于演示
            'imgsz': 640,
            'batch': 2 if device == 'cpu' else 8,  # CPU使用小batch
            'device': device,
            'project': 'runs/train',
            'name': 'flowercount_yolo',
            'exist_ok': True,
            'verbose': True,
            'save': True,
            'plots': True
        }
        
        print("   🔄 开始训练...")
        print(f"   📊 训练参数: epochs={train_args['epochs']}, batch={train_args['batch']}, imgsz={train_args['imgsz']}")
        
        # 开始训练
        results = model.train(**train_args)
        
        print("   ✅ 训练完成!")
        
        # 保存训练结果
        training_results = {
            'model_path': str(model.trainer.best),
            'results': str(results),
            'device': device,
            'epochs': train_args['epochs'],
            'batch_size': train_args['batch'],
            'image_size': train_args['imgsz'],
            'timestamp': datetime.now().isoformat()
        }
        
        results_file = Path("training_results_basic.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(training_results, f, indent=2, ensure_ascii=False)
        
        print(f"   💾 结果保存: {results_file}")
        
        return model, results
        
    except Exception as e:
        print(f"   ❌ 训练失败: {e}")
        return None, None


def test_model(model, device):
    """测试训练好的模型"""
    print("\n🧪 模型测试:")
    print("=" * 15)
    
    if model is None:
        print("   ❌ 没有可用的模型")
        return
    
    try:
        # 获取测试图像
        data_dir = Path("data")
        test_images = list(data_dir.glob('*.jpg'))[:3]  # 测试前3张图像
        
        if not test_images:
            print("   ❌ 没有测试图像")
            return
        
        print(f"   🔍 测试 {len(test_images)} 张图像...")
        
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)
        
        for i, img_path in enumerate(test_images):
            print(f"   📸 测试图像 {i+1}: {img_path.name}")
            
            # 进行推理
            results = model(str(img_path))
            
            # 保存结果
            result_path = results_dir / f"result_{i+1}_{img_path.stem}.jpg"
            results[0].save(str(result_path))
            
            # 打印检测结果
            if len(results[0].boxes) > 0:
                print(f"      🌸 检测到 {len(results[0].boxes)} 个目标")
                for j, box in enumerate(results[0].boxes):
                    conf = box.conf[0].item()
                    print(f"         目标 {j+1}: 置信度 {conf:.3f}")
            else:
                print(f"      ⚠️  未检测到目标")
        
        print(f"   ✅ 测试结果保存在: {results_dir}")
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")


def main():
    """主函数"""
    print_banner()
    
    # 环境检查
    device = check_environment()
    
    # 准备数据集
    dataset_config = prepare_dataset()
    if dataset_config is None:
        print("\n❌ 数据集准备失败")
        return
    
    # 创建演示标注
    if not create_dummy_annotations():
        print("\n❌ 标注创建失败")
        return
    
    # 训练模型
    model, results = train_yolo_model(dataset_config, device)
    
    if model is not None:
        # 测试模型
        test_model(model, device)
        
        print("\n🎉 FlowerCount-YOLO 基础训练完成!")
        print("=" * 40)
        print("📁 训练结果: runs/train/flowercount_yolo")
        print("📁 测试结果: test_results/")
        print("📄 训练日志: training_results_basic.json")
        
        print("\n📋 下一步:")
        print("   1. 查看训练结果和图表")
        print("   2. 使用真实标注数据重新训练")
        print("   3. 调整超参数优化性能")
        print("   4. 部署模型进行实际应用")
        
    else:
        print("\n❌ 训练失败，请检查错误信息")
    
    print("\n🌸 感谢使用FlowerCount-YOLO!")


if __name__ == "__main__":
    main()
