#!/usr/bin/env python3
"""
FlowerCount-YOLO Environment Checker

This script checks if your environment is properly set up for FlowerCount-YOLO.
It verifies all dependencies, CUDA support, and project structure.

Usage:
    python check_environment.py
"""

import sys
import os
import platform
import subprocess
from pathlib import Path
import importlib


def print_banner():
    """Print checker banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    FlowerCount-YOLO Environment Checker                     ║
║                                                                              ║
║    🔍 Comprehensive System and Environment Verification                     ║
║    🌸 Ensuring Optimal Performance for Flower Detection                     ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_system_info():
    """Check basic system information"""
    print("🖥️  System Information:")
    print("=" * 30)
    print(f"   OS: {platform.system()} {platform.release()}")
    print(f"   Architecture: {platform.machine()}")
    print(f"   Python: {sys.version}")
    print(f"   Working Directory: {Path.cwd()}")
    
    # Check if running as admin (Windows)
    if platform.system() == "Windows":
        try:
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            print(f"   Administrator: {'Yes' if is_admin else 'No'}")
        except:
            print(f"   Administrator: Unknown")
    
    print()


def check_conda_environment():
    """Check conda environment"""
    print("🐍 Conda Environment:")
    print("=" * 25)
    
    # Check if conda is available
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"   ✅ Conda: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("   ❌ Conda not found!")
        return False
    
    # Check current environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'base')
    print(f"   📦 Current Environment: {conda_env}")
    
    # List available environments
    try:
        result = subprocess.run(['conda', 'env', 'list'], 
                              capture_output=True, text=True, check=True)
        envs = [line.strip() for line in result.stdout.split('\n') 
                if line.strip() and not line.startswith('#')]
        print(f"   📋 Available Environments: {len(envs)}")
        
        # Check for FlowerCount-YOLO environments
        flower_envs = [env for env in envs if 'flower' in env.lower()]
        if flower_envs:
            print(f"   🌸 FlowerCount Environments: {len(flower_envs)}")
            for env in flower_envs:
                print(f"      - {env}")
        else:
            print("   ⚠️  No FlowerCount environments found")
    except:
        print("   ⚠️  Could not list environments")
    
    print()
    return True


def check_python_packages():
    """Check required Python packages"""
    print("📦 Python Packages:")
    print("=" * 22)
    
    # Core packages
    core_packages = [
        ('torch', 'PyTorch'),
        ('torchvision', 'TorchVision'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('matplotlib', 'Matplotlib'),
        ('pandas', 'Pandas'),
        ('sklearn', 'Scikit-learn'),
        ('yaml', 'PyYAML'),
        ('PIL', 'Pillow'),
        ('tqdm', 'TQDM')
    ]
    
    # Optional packages
    optional_packages = [
        ('ultralytics', 'Ultralytics YOLO'),
        ('wandb', 'Weights & Biases'),
        ('tensorboard', 'TensorBoard'),
        ('transformers', 'Transformers'),
        ('grad_cam', 'GradCAM')
    ]
    
    core_success = 0
    optional_success = 0
    
    print("   Core Packages:")
    for module, name in core_packages:
        try:
            pkg = importlib.import_module(module)
            version = getattr(pkg, '__version__', 'Unknown')
            print(f"   ✅ {name}: {version}")
            core_success += 1
        except ImportError:
            print(f"   ❌ {name}: Not installed")
    
    print("\n   Optional Packages:")
    for module, name in optional_packages:
        try:
            pkg = importlib.import_module(module)
            version = getattr(pkg, '__version__', 'Unknown')
            print(f"   ✅ {name}: {version}")
            optional_success += 1
        except ImportError:
            print(f"   ⚠️  {name}: Not installed")
    
    print(f"\n   📊 Summary: {core_success}/{len(core_packages)} core, {optional_success}/{len(optional_packages)} optional")
    print()
    
    return core_success >= len(core_packages) * 0.8  # 80% success rate


def check_pytorch_cuda():
    """Check PyTorch CUDA support"""
    print("🔥 PyTorch CUDA Support:")
    print("=" * 28)
    
    try:
        import torch
        
        print(f"   📦 PyTorch Version: {torch.__version__}")
        print(f"   🔥 CUDA Available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"   📊 CUDA Version: {torch.version.cuda}")
            print(f"   🎯 GPU Count: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   🎮 GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
            # Test CUDA functionality
            try:
                x = torch.randn(100, 100).cuda()
                y = torch.randn(100, 100).cuda()
                z = torch.mm(x, y)
                print(f"   ✅ CUDA Test: Passed")
                return True
            except Exception as e:
                print(f"   ❌ CUDA Test: Failed ({e})")
                return False
        else:
            print("   ⚠️  CUDA not available - will use CPU")
            return False
            
    except ImportError:
        print("   ❌ PyTorch not installed!")
        return False
    
    print()


def check_nvidia_driver():
    """Check NVIDIA driver"""
    print("🎮 NVIDIA Driver:")
    print("=" * 20)
    
    try:
        result = subprocess.run(['nvidia-smi'], 
                              capture_output=True, text=True, check=True)
        
        # Parse nvidia-smi output
        lines = result.stdout.split('\n')
        for line in lines:
            if 'Driver Version' in line:
                driver_info = line.strip()
                print(f"   ✅ {driver_info}")
                break
        
        # Show GPU utilization
        for line in lines:
            if 'MiB' in line and '/' in line and 'Default' not in line:
                gpu_info = ' '.join(line.split())
                if 'MiB' in gpu_info:
                    print(f"   📊 GPU Memory: {gpu_info}")
                    break
        
        return True
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("   ❌ NVIDIA driver not found or nvidia-smi not available")
        print("   💡 Install NVIDIA drivers from: https://www.nvidia.com/drivers/")
        return False
    
    print()


def check_project_structure():
    """Check project file structure"""
    print("🏗️  Project Structure:")
    print("=" * 24)
    
    required_files = [
        'quick_start.py',
        'main_experiment.py',
        'start_project.py',
        'requirements.txt',
        'configs/experiment_config.yaml',
        'src/models/flowercount_yolo.py',
        'src/data/auto_annotation.py',
        'src/training/flower_trainer.py',
        'src/evaluation/flower_metrics.py',
        'src/visualization/explainability.py'
    ]
    
    required_dirs = [
        'src',
        'src/models',
        'src/data', 
        'src/training',
        'src/evaluation',
        'src/visualization',
        'configs',
        'data'
    ]
    
    files_ok = 0
    dirs_ok = 0
    
    print("   📄 Required Files:")
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
            files_ok += 1
        else:
            print(f"   ❌ {file_path}")
    
    print("\n   📁 Required Directories:")
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"   ✅ {dir_path}/")
            dirs_ok += 1
        else:
            print(f"   ❌ {dir_path}/")
    
    # Check data directory
    data_dir = Path("data")
    if data_dir.exists():
        image_files = list(data_dir.glob('*.jpg')) + list(data_dir.glob('*.png')) + \
                     list(data_dir.glob('*.JPG')) + list(data_dir.glob('*.PNG'))
        print(f"\n   🖼️  Images in data/: {len(image_files)}")
    else:
        print(f"\n   ⚠️  No data directory found")
    
    print(f"\n   📊 Summary: {files_ok}/{len(required_files)} files, {dirs_ok}/{len(required_dirs)} directories")
    print()
    
    return files_ok >= len(required_files) * 0.8 and dirs_ok >= len(required_dirs) * 0.8


def generate_recommendations():
    """Generate setup recommendations"""
    print("💡 Recommendations:")
    print("=" * 20)
    
    # Check current environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'base')
    
    if 'flower' not in conda_env.lower():
        print("   🔧 Create FlowerCount-YOLO environment:")
        print("      conda create -n flowercount-yolo-cuda python=3.9 -y")
        print("      conda activate flowercount-yolo-cuda")
    
    # Check PyTorch
    try:
        import torch
        if not torch.cuda.is_available():
            print("   🔥 Install PyTorch with CUDA:")
            print("      conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y")
    except ImportError:
        print("   📦 Install PyTorch:")
        print("      conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y")
    
    # Check data
    data_dir = Path("data")
    if not data_dir.exists() or not list(data_dir.glob('*.jpg')):
        print("   📁 Prepare data:")
        print("      mkdir data")
        print("      # Copy your flower images to data/ directory")
    
    print("   🚀 Quick start:")
    print("      python quick_start.py --mode demo")
    
    print()


def main():
    """Main checker function"""
    print_banner()
    
    # Run all checks
    checks = [
        ("System Info", check_system_info),
        ("Conda Environment", check_conda_environment),
        ("Python Packages", check_python_packages),
        ("PyTorch CUDA", check_pytorch_cuda),
        ("NVIDIA Driver", check_nvidia_driver),
        ("Project Structure", check_project_structure)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            if check_name == "System Info":
                check_func()  # This one doesn't return a result
                results.append(True)
            else:
                result = check_func()
                results.append(result)
        except Exception as e:
            print(f"   ❌ Error in {check_name}: {e}")
            results.append(False)
    
    # Summary
    print("📋 Environment Check Summary:")
    print("=" * 32)
    
    check_names = ["System Info", "Conda Environment", "Python Packages", 
                  "PyTorch CUDA", "NVIDIA Driver", "Project Structure"]
    
    passed = 0
    for i, (name, result) in enumerate(zip(check_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n   📊 Overall: {passed}/{len(results)} checks passed")
    
    if passed >= len(results) * 0.8:
        print("\n🎉 Environment is ready for FlowerCount-YOLO!")
        print("🚀 You can start with: python quick_start.py --mode demo")
    else:
        print("\n⚠️  Environment needs setup. See recommendations below:")
        generate_recommendations()
    
    print("\n🌸 Happy flower counting!")


if __name__ == "__main__":
    main()
